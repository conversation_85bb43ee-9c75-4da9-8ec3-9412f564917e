import { ChangeDetectorRef, Component, HostListener, OnInit, ViewChild, ViewEncapsulation } from "@angular/core";
import { FormControl } from "@angular/forms";
import { Router } from "@angular/router";
import { CoreConfigService } from "@core/services/config.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AuthenticationService } from "app/auth/service";
import { CacheDataService } from "app/auth/service/cache-data.service";
import { ChangeDataService } from "app/auth/service/change-data.service";
import { DocumentStatus } from "app/models/DocumentStatus";
import { FormType } from "app/models/FormType";
import { WorkSpace } from "app/models/WorkSpace";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { debounceTime, distinctUntilChanged, takeUntil } from "rxjs/operators";
import Swal from "sweetalert2";
import * as feather from 'feather-icons';
import { QuanLyVanBanService } from "./quan-ly-van-ban.service";
import { ShepherdService } from "angular-shepherd";
@Component({
  selector: "app-quan-ly-van-ban",
  templateUrl: "./quan-ly-van-ban.component.html",
  styleUrls: ["./quan-ly-van-ban.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class QuanLyVanBanComponent implements OnInit {
  @ViewChild("editDocumentNameModal") editDocumentNameModal!: any;
  @ViewChild("modalConvertFile") modalConvertFile!: any;
  @ViewChild("modalSearchFile") modalSearchFile!: any;
  @ViewChild("modalCompareFile") modalCompareFile!: any;
  @ViewChild("modalChatbot") modalChatbot!: any;
  @ViewChild("modalWorkSpace") modalWorkSpace!: any;

  public totalFile: number = 0;
  public pageBasic = 1;
  public currentPage = 1;
  public page_size: number = 12;
  public documentStatus = DocumentStatus;
  public _unsubscribeAll: Subject<any> = new Subject();
  public search: FormControl = new FormControl("");
  public listWorkSpace: WorkSpace[];
  public listWorkSpaceFilter: WorkSpace[];
  public type: FormType;
  public title: string;
  public row: any;
  public totalWorkSpace: number = 0;
  public limit: number[] = [10, 15, 20, 25, 30];
  public sortProjectList = ["Tên dự án", "Thời gian tạo", "Số lượng tài liệu"];
  public typeSort = true; // true: asc, false: desc
  public currentSortKey: "name" | "created_at" | "doc_count" = "name";
  public isShowDashboard: boolean = false;
  public role: string;
  public backBtnClass = "btn btn-sm btn-outline-primary";
  public nextBtnClass = "btn btn-sm btn-primary btn-next";
  public isNewUser: boolean = false;
  public isSearchActive = false;
  isMobile: boolean = false;
  constructor(
    private quanlyvanban: QuanLyVanBanService,
    private router: Router,
    private changeData: ChangeDataService,
    private modalService: NgbModal,
    private cacheDataService: CacheDataService,
    private toast: ToastrService,
    private authenService: AuthenticationService,
    private configApp: CoreConfigService,
    private shepherdService: ShepherdService
  ) {
    this.role = this.authenService.currentUserValue.role;
    this.isNewUser = JSON.parse(localStorage.getItem("isNewUser"));
  }

  ngOnInit(): void {
    if (this.role == "USER")
      this.configApp.setConfig({
        layout: { menu: { hidden: true } },
      });
    this.changeData.changeData
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((res) => {
        if (res) {
          this.getAllWorkSpace();
        }
      });
    this.search.valueChanges
      .pipe(
        takeUntil(this._unsubscribeAll),
        debounceTime(500),
        distinctUntilChanged()
      )
      .subscribe((res) => {
        this.getAllWorkSpace();
      });

    this.getAllWorkSpace();
    this.checkScreenSize();
  }
  getAllWorkSpace() {
    this.quanlyvanban
      .getAllWorkSpace(this.search.value, this.currentPage, this.page_size)
      .subscribe((res) => {
        this.listWorkSpace = res.results;
        this.listWorkSpaceFilter = res.results;
        this.totalWorkSpace = res.count;
      });
  }
  addWorkSpace() {
    this.modalOpen(
      this.modalWorkSpace,
      "Thêm dự án",
      null,
      FormType.Create,
      "sm"
    );
  }
  editWorkSpace(workSpace) {
    this.modalOpen(
      this.modalWorkSpace,
      "Cập nhật dự án",
      workSpace,
      FormType.Update,
      "sm"
    );
  }
  modalOpen(modalSM, title: string, row: any, type: FormType, size) {
    this.modalService.open(modalSM, {
      centered: true,
      size: size,
    });
    this.title = title;
    this.type = type;
    this.row = row;
  }
  deleteWorkSpace(item) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this.quanlyvanban.deleteWorkSpace(item.id).subscribe(
          (res) => {
            this.toast.success("Đã xoá không gian làm việc", "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });

            this.getAllWorkSpace();
          },
          (error) => {
            this.toast.error(error.message, "Thất bại", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          }
        );
      }
    });
  }
  detailWorkSpace(workSpace: WorkSpace) {
    this.router.navigate([`/quan-ly-van-ban/workspace/${workSpace.id}`], {
      queryParams: { workSpaceName: workSpace.name },
    });
  }

  onPageChange(e) {
    this.currentPage = e;
    this.getAllWorkSpace();
  }
  sortProject(event) {
    // Xác định trường sort dựa trên lựa chọn
    switch (event) {
      case "Tên dự án":
        this.currentSortKey = "name";
        break;
      case "Thời gian tạo":
        this.currentSortKey = "created_at";
        break;
      case "Số lượng tài liệu":
        this.currentSortKey = "doc_count";
        break;
    }
    this.sortList(this.currentSortKey, this.typeSort);
  }

  sortList(key: "name" | "created_at" | "doc_count", asc: boolean = true) {
    this.listWorkSpaceFilter = this.listWorkSpace.slice().sort((a, b) => {
      let result = 0;
      if (key === "name") {
        result = a.name.localeCompare(b.name);
      } else if (key === "created_at") {
        result =
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      } else if (key === "doc_count") {
        result = a.doc_count - b.doc_count;
      }
      return asc ? result : -result;
    });
  }

  loaiSapXep() {
    this.typeSort = !this.typeSort;
    this.sortList(this.currentSortKey, this.typeSort);
  }
  showConvertFile() {
    this.modalOpen(
      this.modalConvertFile,
      "Chuyển đổi văn bản",
      null,
      FormType.Create,
      "xl"
    );
  }
  showSearchFile() {
    this.cacheDataService.clearCache();
    this.modalOpen(
      this.modalSearchFile,
      "Tìm kiếm văn bản",
      null,
      FormType.Search,
      "xl"
    );
  }
  showCompareFile() {
    this.modalOpen(
      this.modalCompareFile,
      "So sánh văn bản",
      null,
      FormType.Compare,
      "xl"
    );
  }
  showChatbot() {
    const workspaceId = localStorage.getItem("workspace_id");
    if (!workspaceId) {
      this.authenService.logout();
      this.router.navigate(["pages/authentication/login-v2"]);
    } else {
      this.modalOpen(
        this.modalChatbot,
        "Chuyển đổi văn bản",
        null,
        FormType.Chatbot,
        "xl"
      );
    }
  }
  startTour() {
    this.shepherdService.start();
  }
  ngAfterViewInit() {
    feather.replace();
    // tour steps
    this.shepherdService.defaultStepOptions = {
      cancelIcon: {
        enabled: true,
      },
    };
    this.shepherdService.modal = true;

    this.shepherdService.addSteps([
      {
        title: "Cụm tính năng",
        text: "Bao gồm các công cụ tìm kiếm, chuyển đổi và so sánh văn bản, đáp ứng nhu cầu tra cứu và phân tích nội dung văn bản quy phạm pháp luật.",
        attachTo: {
          element: ".step-1",
          on: "bottom",
        },
        buttons: [
          {
            text: "Tiếp tục",
            type: "next",
            classes: "btn btn-sm btn-primary btn-next ml-auto",
          },
        ],
        useModalOverlay: true,
      },
      {
        title: "Tính năng Truy vấn Chatbot",
        text: "Đây là tính năng trò chuyện, đặt câu hỏi, truy vấn về các vấn đề pháp luật với câu trả lời đầy đủ rõ ràng chi tiết dễ hiểu.",
        attachTo: {
          element: ".chat-button",
          on: "top",
        },
        buttons: [
          {
            text: "Quay lại",
            type: "back",
            classes: this.backBtnClass,
          },
          {
            text: "Tiếp tục",
            type: "next",
            classes: this.nextBtnClass,
          },
        ],
      },
      {
        title: "Tạo không gian dự án",
        text: "Tạo không gian dự án để tổ chức, lưu trữ và làm việc với tài liệu liên quan một cách khoa học, dễ quản lý.",
        attachTo: {
          element: ".step-3",
          on: "top",
        },
        buttons: [
          {
            text: "Quay lại",
            type: "back",
            classes: this.backBtnClass,
          },
          {
            text: "Thêm dự án",
            classes: this.nextBtnClass,
            action: () => {
              this.quanlyvanban.isShowTourGuide.next(true);
              this.addWorkSpace();
              this.shepherdService.next();
            },
          },
        ],
      },
    ]);
  }


  @HostListener('window:resize', [])
  onResize() {
    this.checkScreenSize();
  }

  private checkScreenSize(): void {
    this.isMobile = window.innerWidth <= 1200; // hoặc breakpoint bạn muốn
  }
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
    this.changeData.changeData.next(false);
    this.cacheDataService.clearCache();
  }
}
