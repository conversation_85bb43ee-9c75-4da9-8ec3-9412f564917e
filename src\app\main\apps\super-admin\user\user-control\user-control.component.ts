import { Component, OnInit, ViewChild, ViewEncapsulation } from "@angular/core";
import { <PERSON><PERSON><PERSON>er, FormGroup, NgForm, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { AuthenticationService } from "app/auth/service";
import { emailValidator } from "app/shared/EmailValidator";
import { FlatpickrOptions } from "ng2-flatpickr";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { FormatTime } from "../../../../../../../util/formatTime";
import { OrganizationService } from "../../organization/organization.service";
import { UserService } from "../user.service";

@Component({
  selector: "app-user-control",
  templateUrl: "./user-control.component.html",
  styleUrls: ["./user-control.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class UserControlComponent implements OnInit {
  @ViewChild("formDirective") private formDirective: NgForm;
  @ViewChild("flatpickrRef", { static: false }) flatpickrElement: any;
  public breadcrumbDefault: object;
  public userForm: FormGroup;
  public type: string;
  public basicDateOptions: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
    mode: "single",
    maxDate: "today",
  };
  public gender = ["Nam", "Nữ"];
  public submitted: boolean = false;
  public formData: FormData;
  public avatarUrl: string = "";
  public _unSubAll: Subject<any> = new Subject();
  public userId: string;
  public fileAvatar: any;
  public listOrgan = [];
  public selectedOrganization;
  public listPosition = [];
  public listRole = [
    { id: "member", label: "Người dùng" },
    // { id: "admin", label: "Quản trị viên" },
  ];
  public mergedPwdShow = false;
  public role: string;
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private _userService: UserService,
    private toast: ToastrService,
    private _organService: OrganizationService,
    private _authenService: AuthenticationService
  ) {
    this.userForm = this.fb.group({
      email: [null, [Validators.required, emailValidator]],
      password: [
        null,
        [
          Validators.required,
          Validators.minLength(6),
          Validators.pattern(
            /^(?=.*[A-Z])(?=.*[!@#$%^&*()_+{}\[\]:;<>,.?~\\/-]).{6,}$/
          ),
          Validators.maxLength(255),
        ],
      ],

      fullname: [null, [Validators.required, Validators.maxLength(255)]],
      gender: [null],
      phone: [
        null,
        [Validators.pattern(/^0\d{9}$/), Validators.maxLength(255)],
      ],
      position: [null],
      role: ["member"],
      dob: [null],
      address: [null, Validators.maxLength(255)],
      organization: [null],
    });
    this.role = _authenService.currentUserValue.role;
  }

  ngOnInit(): void {
    this.type = this.route.snapshot.queryParams.type;

    this.breadcrumbDefault = {
      links: [
        {
          name: "Quản lý người dùng",
          isHeader: true,
        },
        {
          name: this.type == "add" ? "Thêm người dùng" : "Cập nhật người dùng",
        },
      ],
    };
    this._userService.data.pipe(takeUntil(this._unSubAll)).subscribe((res) => {
      if (res) {
        this.userId = res.id;
        this.avatarUrl = res.avatar;
        this.userForm.patchValue({
          ...res,
          position: res.position?.id,
          organization: res.organization_memberships[0]?.organization_id,
          role: res.system_role == "USER" ? "member" : "admin",
        });
        this.basicDateOptions.defaultDate = new Date(res.dob);
        this.getPositionFromOrgan(
          res.organization_memberships[0]?.organization_id
        );
      } else {
        this.getPositionFromOrgan("");
        // this.router.navigate(["super-admin/user"]);
      }
    });
    this.getAllOrgan();
    if (this.role == "ADMIN" || this.type == "update") {
      this.listRole = [
        { id: "member", label: "Người dùng" },
        { id: "admin", label: "Quản trị viên" },
      ];
    }
  }
  getAllOrgan() {
    const params: any = {};

    if (this.role === "ADMIN") {
      params["root_organization_id"] = localStorage.getItem("organization_id");
      if (this.type !== "update") {
        params["status"] = "active";
      }
    }

    this._organService.get(params).subscribe((res) => {
      const data = res.results ?? res;
      this.listOrgan = this.buildFlatTreeForNgSelect(data);
      this.selectedOrganization = data[0];
    });
  }
  
  getPositionFromOrgan(idOrgan) {
    this._userService.getPosition(idOrgan).subscribe((res) => {
      this.listPosition = res.results;
      if (res.results) {
        this.listPosition = res.results;
      } else {
        this.listPosition = res;
      }
    });
  }
  get f() {
    return this.userForm.controls;
  }
  onAvatarSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];
      const maxSizeMB = 5;
      const maxSizeBytes = maxSizeMB * 1024 * 1024;
      if (file.size > maxSizeBytes) {
        this.toast.error("Vui lòng chọn ảnh dưới 5MB!", "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        input.value = ""; // Reset file input
        return;
      }
      // this.formData.append("avatar", file);
      this.fileAvatar = file;
      const reader = new FileReader();

      reader.onload = () => {
        this.avatarUrl = reader.result as string;
        // this.userForm.patchValue({ avatar: this.avatarUrl });
      };

      reader.readAsDataURL(file);
    }
  }
  onSubmit() {
    this.submitted = true;
    this.formData = new FormData();
    if (this.fileAvatar) this.formData.append("avatar", this.fileAvatar);
    if (this.type == "update") {
      this.userForm.get("password").clearValidators();
      this.userForm.get("password").updateValueAndValidity();
    }
    if (this.userForm.invalid) {
      return;
    }

    const dateValue = new FormatTime().formatDateTime(
      new Date(this.userForm.get("dob").value),
      "yyyy-MM-dd"
    );
    if (dateValue != "") this.userForm.patchValue({ dob: dateValue });
    for (const key in this.userForm.value) {
      if (
        this.userForm.value.hasOwnProperty(key) &&
        this.userForm.value[key] !== null &&
        this.userForm.value[key] !== undefined
      ) {
        this.formData.append(key, this.userForm.value[key]);
      }
    }
    if (this.type == "add") {
      this._userService.createUser(this.formData).subscribe(
        (res) => {
          this.toast.success("Thêm người dùng", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.userForm.reset();
          this.router.navigate(["super-admin/user"]);
        },
        (error) => {
          // this.toast.error(error.error, "Thất bại", {
          //   closeButton: true,
          //   positionClass: "toast-top-right",
          //   toastClass: "toast ngx-toastr",
          // });
        }
      );
    } else {
      this._userService.update(this.userId, this.formData).subscribe(
        (res) => {
          this.toast.success("Cập nhật người dùng", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.userForm.reset();
          this.router.navigate(["super-admin/user"]);
        },
        (error) => {
          // this.toast.error(error.error, "Thất bại", {
          //   closeButton: true,
          //   positionClass: "toast-top-right",
          //   toastClass: "toast ngx-toastr",
          // });
        }
      );
    }
  }

  onCancel() {
    this.router.navigate(["super-admin/user"]);
  }
  changeOrganization(event) {
    let organId;
    if (event) {
      organId = event.id;
      this.listRole = [
        { id: "member", label: "Người dùng" },
        { id: "admin", label: "Quản trị viên" },
      ];
      this.getPositionFromOrgan(organId);
    } else {
      this.listRole = [{ id: "member", label: "Người dùng" }];
      if (this.role == "SUPER_ADMIN")
        this.userForm.patchValue({ role: "member" });
    }
    this.userForm.patchValue({ position: null });
  }
  buildFlatTreeForNgSelect(data: any[]): [] {
    // Bước 1: Tạo map id → node
    const nodeMap: { [id: string]: any } = {};
    const roots: any[] = [];

    data.forEach((org) => {
      nodeMap[org.id] = { id: org.id, name: org.name, children: [] };
    });

    // Bước 2: Gắn cha – con
    data.forEach((org) => {
      const node = nodeMap[org.id];
      if (org.parent_organization && nodeMap[org.parent_organization]) {
        nodeMap[org.parent_organization].children!.push(node);
      } else {
        roots.push(node);
      }
    });

    // Bước 3: Duyệt cây và tạo danh sách phẳng với indent
    const result: any = [];

    function traverse(node: any, level: number) {
      result.push({
        id: node.id,
        name: `${"— ".repeat(level)}${node.name}`,
      });
      node.children?.forEach((child) => traverse(child, level + 1));
    }

    roots.forEach((root) => traverse(root, 0));
    return result;
  }

  ngOnDestroy() {
    this._unSubAll.next(null);
    this._unSubAll.complete();
    this._userService.data.next(null);
  }
}
