FROM node:14.21.3-bullseye as build

WORKDIR /usr/local/app
RUN npm i -g @angular/cli@14.0.6

COPY ["package.json", "./"]

RUN npm install

COPY ./ /usr/local/app/

RUN ng build --configuration devui --output-path=dist/cls --base-href /cls/ --deploy-url /cls/

FROM nginx:latest as deploy

COPY /nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /usr/local/app/dist/ /usr/share/nginx/html
RUN rm /usr/share/nginx/html/index.html
RUN cp /usr/share/nginx/html/cls/index.html /usr/share/nginx/html/index.html
EXPOSE 80
WORKDIR usr/local/app
COPY replace_tag.sh .
CMD bash /usr/local/app/replace_tag.sh && nginx -g 'daemon off;'