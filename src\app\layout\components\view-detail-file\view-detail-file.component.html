<div class="">
  <div
    class="view-detail-file-header-border d-flex align-items-center p-xxl-1 p-xl-50"
  >
    <span
      *ngIf="type != FormType.Convert"
      class="cursor-pointer text-primary mr-1"
      (click)="goBackSearch()"
      ngbTooltip="Quay lại"
      container="body"
      ><i data-feather="arrow-left-circle" size="24"></i
    ></span>
    <div
      class="w-100 d-flex justify-content-between align-items-center font-sm"
    >
      <b
        class="m-0 one-line"
        [ngbTooltip]="fileName"
        container="body"
        placement="bottom"
        >{{ fileName }}</b
      >
      <div>
        <button
          *ngIf="
            typeDocument !== 'upload' &&
            save == 'true' &&
            type != FormType.Convert
          "
          class="view-detail-file-save-button btn round btn-outline-secondary btn-sm font-sm"
          rippleEffect
          (click)="saveFileToListDocument()"
        >
          L<PERSON>u
        </button>
        <span
          class="cursor-pointer"
          (click)="modal.dismiss('Cross click')"
          *ngIf="type == FormType.Convert || type == FormType.Maximized"
        >
          <img src="assets/images/icons/x.svg" alt="x" />
        </span>
      </div>
    </div>
  </div>

  <div class="card mb-0" *ngIf="dataFile">
    <div
      class="card-body pt-0"
      [ngStyle]="{
        height: type == FormType.Convert ? '85vh' : ''
      }"
    >
      <div class="row">
        <div class="col-12">
          <!-- <h3 class="m-0">Tên tài liệu: {{ this.fileName }}</h3> -->
          <ul
            ngbNav
            #nav="ngbNav"
            class="nav-tabs font-sm mb-0"
            (navChange)="changeNav($event)"
            [activeId]="avtiveTab"
          >
            <li ngbNavItem [ngbNavItem]="'toanvan'">
              <a ngbNavLink>Toàn văn</a>
              <ng-template ngbNavContent>
                <div class="view-detail-file-content-container">
                  <div
                    #contentDiv
                    class="view-detail-file-content-wrapper custom-html-content p-50"
                    (mouseup)="onTextSelect()"
                    (scroll)="onScrollContent()"
                    *ngIf="dataFile.toan_van && safeHtml && !isShowFileOriginal"
                    [innerHTML]="safeHtml"
                  ></div>
                  <div
                    *ngIf="showPopover"
                    class="view-detail-file-popover popover-box"
                    [ngStyle]="{
                      top: popoverPosition.top + 'px',
                      left: popoverPosition.left + 'px',
                      'z-index': 9999
                    }"
                  >
                    <div
                      (click)="copyText()"
                      class="cursor-pointer function-item"
                    >
                      <img
                        src="assets/images/icons/copy.svg"
                        alt="copy"
                        class="mr-1"
                      />Sao chép
                    </div>
                    <div
                      *ngIf="type != FormType.Convert"
                      (click)="askChatbot()"
                      class="cursor-pointer function-item"
                    >
                      <img
                        src="assets/images/icons/text-scan.svg"
                        alt="scan"
                        class="mr-1"
                      />Hỏi Chatbot
                    </div>
                    <div
                      *ngIf="type != FormType.Convert"
                      (click)="createNote()"
                      class="cursor-pointer function-item"
                    >
                      <span class="mr-1">
                        <i data-feather="file" size="19"></i>
                      </span>
                      Tạo ghi chú nhanh
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>
            <li ngbNavItem [ngbNavItem]="'vanbangoc'" *ngIf="dataFile.docx_url">
              <a ngbNavLink>Văn bản gốc</a>
              <ng-template ngbNavContent>
                <ng-container [ngSwitch]="getFileExtension(dataFile.docx_url)">
                  <ngx-extended-pdf-viewer
                    *ngSwitchCase="'pdf'"
                    [src]="dataFile.docx_url"
                    [useBrowserLocale]="true"
                    [showBorders]="false"
                    [showSidebarButton]="false"
                    [sidebarVisible]="false"
                    pageViewMode="multiple"
                    backgroundColor="gray"
                    [ignoreKeys]="['Ctrl+WHEEL']"
                    [showToolbar]="false"
                    (pdfLoaded)="contentLoaded()"
                    (pdfLoadingFailed)="loadFileError = true; isLoading = false"
                  >
                  </ngx-extended-pdf-viewer>
                  <div>
                    <div *ngIf="isLoading" class="loading-overlay">
                      <p class="m-0">Đang tải tài liệu...</p>
                    </div>
                    <div *ngIf="loadFileError" class="loading-overlay">
                      <p class="m-0">Tải tài liệu lỗi, vui lòng thử lại ...</p>
                    </div>
                    <ngx-doc-viewer
                      class="view-detail-file-doc-viewer"
                      *ngSwitchDefault
                      (loaded)="contentLoaded()"
                      [url]="dataFile.docx_url"
                      viewer="office"
                    >
                    </ngx-doc-viewer>
                  </div>
                </ng-container>
              </ng-template>
            </li>
            <li
              ngbNavItem
              [ngbNavItem]="'tomtatvanban'"
              *ngIf="typeDocument == 'upload'"
            >
              <a ngbNavLink>Tóm tắt</a>
              <ng-template ngbNavContent>
                <div class="view-detail-file-content-container">
                  <div
                    class="view-detail-file-summary-wrapper custom-html-content p-50 mt-1 wrap-text w-100"
                  >
                    <h3>Tóm tắt văn bản: {{ fileName }}</h3>
                    <p
                      class="gradient-text"
                      *ngIf="isStreaming || statusStreaming == 2"
                    >
                      Đang tóm tắt
                    </p>
                    <p
                      #sumarizeContent
                      class="view-detail-file-summary-content"
                      [attr.contenteditable]="isEditSumarize"
                      [attr.id]="'msg-tomtat'"
                      [innerHTML]="cleanSummarize(dataSummarize) | markdown"
                      (input)="editSumarize($event)"
                    ></p>
                    <p
                      class="gradient-text"
                      *ngIf="!isStreaming && statusStreaming == 0"
                    >
                      Tóm tắt thất bại
                    </p>
                    <div
                      class="action-chat"
                      *ngIf="!isStreaming && statusStreaming == 1"
                    >
                      <span
                        *ngIf="dataSummarize != 'Không có nội dung tóm tắt'"
                        class="cursor-pointer p-50 icon-button"
                        appcopy
                        [copyTarget]="'#msg-tomtat'"
                        ><img
                          src="assets/images/icons/copy.svg"
                          ngbTooltip="Sao chép"
                          container="body"
                          alt="copy"
                      /></span>
                      <span
                        *ngIf="streamingDone"
                        class="cursor-pointer p-50 icon-button"
                        (click)="saveSummarize(dataSummarize)"
                        ><img
                          src="assets/images/icons/tick2.svg"
                          ngbTooltip="Lưu kết quả"
                          container="body"
                          alt="tick2"
                      /></span>
                      <span
                        *ngIf="
                          dataSummarize != 'Không có nội dung tóm tắt' &&
                          isEditSumarize
                        "
                        class="cursor-pointer p-50 icon-button"
                        (click)="saveSummarizeEdited()"
                        ><img
                          src="assets/images/icons/tick2.svg"
                          ngbTooltip="Lưu kết quả chỉnh sửa"
                          container="body"
                          alt="tick2"
                      /></span>
                      <span
                        *ngIf="
                          dataSummarize != 'Không có nội dung tóm tắt' &&
                          isEditSumarize
                        "
                        class="cursor-pointer p-50 icon-button"
                        (click)="cancelSummarizeEdited()"
                        ><img
                          src="assets/images/icons/x.svg"
                          ngbTooltip="Huỷ chỉnh sửa"
                          container="body"
                          alt="x"
                      /></span>
                      <span
                        *ngIf="
                          dataSummarize != 'Không có nội dung tóm tắt' &&
                          !isEditSumarize
                        "
                        class="cursor-pointer p-50 icon-button"
                        (click)="isEditSumarize = true"
                        ><img
                          src="assets/images/icons/pencil.svg"
                          ngbTooltip="Chỉnh sửa"
                          container="body"
                          alt="pencil"
                      /></span>
                      <span
                        class="cursor-pointer"
                        ngbTooltip="Tóm tắt lại"
                        container="body"
                        (click)="streamSumarize()"
                        ><img
                          src="assets/images/icons/refresh.svg"
                          alt="refresh"
                          class="icon-button"
                      /></span>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>
            <li ngbNavItem [ngbNavItem]="'tongquan'">
              <a ngbNavLink>Tổng quan</a>
              <ng-template ngbNavContent>
                <div class="w-100 d-flex justify-content-end">
                  <!-- *ngIf="typeDocument != 'search'" -->
                  <button
                    *ngIf="typeDocument == 'upload'"
                    type="button"
                    class="btn btn-outline-secondary round mb-1"
                    rippleEffect
                    (click)="
                      editTongQuan(soHieuRef, coQuanBanHanhRef, phamViRef)
                    "
                  >
                    {{ isEditTongQuan ? "Lưu" : "Chỉnh sửa" }}
                  </button>
                </div>
                <div class="document-table-container">
                  <table class="document-table">
                    <tbody>
                      <tr>
                        <td class="document-table__label">Số ký hiệu</td>
                        <td
                          #soHieuRef
                          class="document-table__value"
                          [contentEditable]="isEditTongQuan"
                        >
                          {{ dataFile.so_hieu }}
                        </td>
                        <td class="document-table__label">Ngày ban hành</td>
                        <td
                          *ngIf="!isEditTongQuan"
                          class="document-table__value"
                        >
                          {{ dataFile.ngay_ban_hanh | safeDate }}
                        </td>
                        <td
                          *ngIf="isEditTongQuan"
                          class="document-table__value"
                        >
                          <ng2-flatpickr
                            [config]="customDateOptions1"
                            name="customDate"
                            placeholder="Tất cả"
                            (change)="selectNgayBanHanh($event)"
                            [(ngModel)]="dataFile.ngay_ban_hanh"
                          ></ng2-flatpickr>
                        </td>
                      </tr>
                      <tr>
                        <td class="document-table__label">Loại văn bản</td>
                        <td
                          *ngIf="!isEditTongQuan"
                          class="document-table__value"
                        >
                          {{ dataFile.loai_van_ban }}
                        </td>
                        <td
                          *ngIf="isEditTongQuan"
                          class="document-table__value"
                        >
                          <ng-select
                            [items]="listLoaiVanBan"
                            bindLabel="label"
                            bindValue="value"
                            [clearable]="false"
                            (change)="changeLoaiVanBan($event)"
                            [(ngModel)]="dataFile.loai_van_ban"
                          >
                          </ng-select>
                          <!-- {{ dataFile.loai_van_ban }} -->
                        </td>
                        <td class="document-table__label">Ngày có hiệu lực</td>
                        <td
                          *ngIf="!isEditTongQuan"
                          class="document-table__value"
                        >
                          {{ dataFile.ngay_co_hieu_luc | safeDate }}
                        </td>
                        <td
                          *ngIf="isEditTongQuan"
                          class="document-table__value"
                        >
                          <ng2-flatpickr
                            [config]="customDateOptions2"
                            name="customDate"
                            placeholder="Tất cả"
                            (change)="selectNgayCoHieuLuc($event)"
                            [(ngModel)]="dataFile.ngay_co_hieu_luc"
                          ></ng2-flatpickr>
                        </td>
                      </tr>
                      <tr>
                        <td class="document-table__label">Nguồn thu thập</td>
                        <td class="document-table__value">
                          {{
                            typeDocument == "csdl" ||
                            typeDocument == "search" ||
                            typeDocument == "searching"
                              ? "Cơ sở dữ liệu"
                              : "Tải lên"
                          }}
                        </td>
                        <td class="document-table__label">
                          Ngày đăng công báo
                        </td>
                        <td
                          *ngIf="!isEditTongQuan"
                          class="document-table__value"
                        >
                          {{ dataFile.ngay_dang_cong_bao | safeDate }}
                        </td>
                        <td
                          *ngIf="isEditTongQuan"
                          class="document-table__value"
                        >
                          <ng2-flatpickr
                            [config]="customDateOptions3"
                            name="customDate"
                            placeholder="Tất cả"
                            (change)="selectNgayDangCongBao($event)"
                            [(ngModel)]="dataFile.ngay_dang_cong_bao"
                          ></ng2-flatpickr>
                        </td>
                      </tr>
                      <tr>
                        <td class="document-table__label">
                          Cơ quan ban hành/ Chức danh/ Người ký
                        </td>
                        <td
                          #coQuanBanHanhRef
                          class="document-table__value"
                          [contentEditable]="isEditTongQuan"
                        >
                          {{
                            dataFile.co_quan_ban_hanh
                              ? dataFile.co_quan_ban_hanh
                              : "Không có"
                          }}
                          /
                          {{
                            dataFile?.chuc_danh
                              ? dataFile?.chuc_danh
                              : "Không có"
                          }}
                          /
                          {{
                            dataFile.nguoi_ky ? dataFile.nguoi_ky : "Không có"
                          }}
                        </td>
                        <td class="document-table__label">Phạm vi</td>
                        <td
                          #phamViRef
                          class="document-table__value"
                          [contentEditable]="isEditTongQuan"
                        >
                          {{ dataFile.pham_vi }}
                        </td>
                      </tr>
                      <tr>
                        <td class="document-table__label">Trích yếu</td>
                        <td
                          class="document-table__value"
                          [attr.colspan]="3"
                          [innerHTML]="dataFile?.trich_yeu | stripHtml"
                        ></td>
                      </tr>
                    </tbody>
                  </table>

                  <div
                    *ngIf="typeDocument != 'upload'"
                    class="document-status"
                    [ngClass]="{
                      'document-status--active':
                        dataFile.tinh_trang_hieu_luc === 'Còn hiệu lực',
                      'document-status--warning':
                        dataFile.tinh_trang_hieu_luc ===
                        'Hết hiệu lực một phần',
                      'document-status--danger':
                        dataFile.tinh_trang_hieu_luc === 'Hết hiệu lực toàn bộ',
                      'document-status--info': ![
                        'Còn hiệu lực',
                        'Hết hiệu lực một phần',
                        'Hết hiệu lực toàn bộ'
                      ].includes(dataFile.tinh_trang_hieu_luc)
                    }"
                  >
                    <span class="document-status__label"
                      >Tình trạng hiệu lực:</span
                    >
                    <span class="document-status__value" #tinhTrangHieuLucRef>{{
                      dataFile.tinh_trang_hieu_luc
                    }}</span>
                  </div>
                </div>
              </ng-template>
            </li>
            <li ngbNavItem [ngbNavItem]="'luocdo'">
              <a ngbNavLink>Lược đồ</a>
              <ng-template ngbNavContent>
                <div class="view-detail-file-luocdo-container">
                  <div
                    class="w-100 d-flex align-items-center justify-content-between"
                  >
                    <div *ngIf="listPanels?.length > 0">
                      <div class="form-group">
                        <input
                          type="text"
                          class="form-control"
                          id="basicInput"
                          placeholder="Lọc kết quả"
                          (keyup)="filterLuocDO($event)"
                        />
                      </div>
                    </div>
                    <button
                      *ngIf="
                        typeDocument == 'upload' && type != FormType.Convert
                      "
                      [ngClass]="
                        isToggleAddClause
                          ? 'btn-primary'
                          : 'btn-outline-secondary'
                      "
                      type="button"
                      class="btn round ml-auto"
                      rippleEffect
                      (click)="addVanBanCanCu()"
                    >
                      Thêm văn bản căn cứ
                    </button>
                  </div>
                  <div
                    class="view-detail-file-luocdo-content collapse-icon"
                    id="detail-file"
                    *ngIf="listPanelsFiltered?.length > 0"
                  >
                    <ngb-accordion
                      #acc="ngbAccordion"
                      [activeIds]="activePanelIds"
                    >
                      <ngb-panel
                        *ngFor="
                          let panel of listPanelsFiltered;
                          let idx = index
                        "
                        [id]="'ngb-panel-' + idx"
                        [open]="idx === 0"
                      >
                        <ng-template ngbPanelTitle>
                          <span class="lead collapse-title card-title">
                            <strong class="font-sm">
                              {{ panel.title }} ({{ panel.list.length }})
                            </strong>
                          </span>
                        </ng-template>

                        <ng-template ngbPanelContent>
                          <div>
                            <ng-container
                              *ngFor="
                                let item of panel.list;
                                let i = index;
                                let last = last
                              "
                            >
                              <div
                                (click)="viewFileLienQuan(item)"
                                class="d-flex align-items-start pl-xxl-1 pt-xxl-1 pl-xl-50 pt-xl-50 folder-item"
                              >
                                <div
                                  class="w-100 d-flex justify-content-between align-items-start"
                                >
                                  <div class="flex-grow-1 pe-2">
                                    <div class="d-flex align-items-center">
                                      <p
                                        class="m-0 font-weight-bolder one-line"
                                        placement="bottom"
                                        [openDelay]="300"
                                        [ngbTooltip]="
                                          item.trich_yeu | stripHtml
                                        "
                                      >
                                        <span *ngIf="i < 9">0</span>{{ i + 1 }}.
                                        <span class="text-primary"
                                          >{{ item.loai_van_ban }} -
                                        </span>
                                        {{ item.so_hieu }} -
                                        <span
                                          [innerHTML]="item.trich_yeu"
                                          [attr.id]="'msg-' + i"
                                        ></span>
                                      </p>
                                      <span
                                        appcopy
                                        [text]="
                                          item.loai_van_ban +
                                            ' - ' +
                                            item.so_hieu +
                                            ' - ' +
                                            item.trich_yeu | stripHtml
                                        "
                                        class="text-primary cursor-pointer ml-50"
                                        ngbTooltip="Sao chép"
                                        container="body"
                                        ><i data-feather="copy"></i
                                      ></span>
                                      <div
                                        *ngIf="typeDocument == 'upload'"
                                        (click)="
                                          deleteVanBanLienQuan(item);
                                          $event.stopPropagation()
                                        "
                                        class="mx-50"
                                        ngbTooltip="Xoá"
                                        container="body"
                                      >
                                        <img
                                          src="assets/images/icons/delete-clause.svg"
                                          alt="deleteClause"
                                          class="icon"
                                        />
                                      </div>
                                    </div>

                                    <div class="wrap-text mt-xxl-1 mt-xl-50">
                                      <div class="d-flex flex-wrap gap-2">
                                        <p *ngIf="item.tinh_trang_hieu_luc">
                                          Hiệu lực:
                                          <span
                                            class="font-weight-bolder"
                                            [ngClass]="{
                                              'text-danger':
                                                item.tinh_trang_hieu_luc ===
                                                'Hết hiệu lực toàn bộ',
                                              'text-success':
                                                item.tinh_trang_hieu_luc ===
                                                'Còn hiệu lực',
                                              'text-warning':
                                                item.tinh_trang_hieu_luc ===
                                                'Hết hiệu lực một phần',
                                              'text-primary':
                                                item.tinh_trang_hieu_luc ===
                                                'Chưa có hiệu lực',
                                              'text-secondary':
                                                item.tinh_trang_hieu_luc ===
                                                'Ngưng hiệu lực một phần',
                                              'text-muted':
                                                item.tinh_trang_hieu_luc ===
                                                'Không còn phù hợp'
                                            }"
                                          >
                                            {{ item.tinh_trang_hieu_luc }}
                                          </span>
                                        </p>
                                        <p *ngIf="item.ngay_ban_hanh">
                                          Ban hành:
                                          <span class="font-weight-bolder">
                                            {{
                                              item.ngay_ban_hanh
                                                | date : "dd-MM-yyyy" : "+0000"
                                            }}
                                          </span>
                                        </p>
                                        <p *ngIf="item.ngay_co_hieu_luc">
                                          Áp dụng:
                                          <span class="font-weight-bolder">
                                            {{
                                              item.ngay_co_hieu_luc
                                                | date : "dd-MM-yyyy" : "+0000"
                                            }}
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <hr *ngIf="!last" />
                            </ng-container>
                          </div>
                        </ng-template>
                      </ngb-panel>
                    </ngb-accordion>
                  </div>
                  <ng-container *ngIf="listPanelsFiltered?.length == 0">
                    <div
                      class="d-flex flex-column justify-content-center align-items-center"
                    >
                      <img
                        src="assets/images/icons/no-file.svg"
                        alt="no-file"
                      />
                      <p class="font-weight-bolder h3 mt-2">
                        Không có thông tin
                      </p>
                    </div>
                  </ng-container>
                </div>
              </ng-template>
            </li>

            <li
              ngbNavItem
              [ngbNavItem]="'lienquannoidung'"
              *ngIf="typeDocument == 'upload'"
            >
              <a ngbNavLink
                >Liên quan nội dung ({{ listVanBanLienQuan?.length }})</a
              >
              <ng-template ngbNavContent>
                <div
                  class="w-100 d-flex align-items-center justify-content-between"
                >
                  <div *ngIf="listVanBanLienQuan?.length > 0">
                    <div class="form-group">
                      <input
                        type="text"
                        class="form-control"
                        id="basicInput"
                        placeholder="Lọc kết quả"
                        (keyup)="filterVanBanLienQuan($event)"
                      />
                    </div>
                  </div>
                  <div
                    class="ml-auto mr-1 cursor-pointer show-button-toggle"
                    [ngbTooltip]="
                      isShowTable
                        ? 'Hiển thị dạng danh sách'
                        : 'Hiển thị dạng bảng'
                    "
                    container="body"
                    (click)="isShowTable = !isShowTable"
                  >
                    <img
                      [src]="
                        isShowTable
                          ? 'assets/images/icons/show-table-active.svg'
                          : 'assets/images/icons/show-table.svg'
                      "
                      alt="show-table"
                    />
                    <span class="show-button-label">{{
                      isShowTable ? "Danh sách" : "Dạng bảng"
                    }}</span>
                  </div>
                  <button
                    *ngIf="type != FormType.Convert"
                    [ngClass]="
                      isToggleAddClause
                        ? 'btn-primary'
                        : 'btn-outline-secondary'
                    "
                    type="button"
                    class="btn round"
                    rippleEffect
                    (click)="addVanBanLienQuan()"
                  >
                    Thêm văn bản liên quan
                  </button>
                </div>
                <div
                  class="view-detail-file-lienquan-container"
                  *ngIf="!isShowTable && listVanBanLienQuanFilter?.length > 0"
                >
                  <div class="view-detail-file-lienquan-content">
                    <ng-container
                      *ngFor="
                        let item of listVanBanLienQuanFilter;
                        let i = index
                      "
                    >
                      <div
                        (click)="viewFileLienQuan(item)"
                        class="d-flex align-items-start pl-xxl-1 pt-xxl-1 pl-xl-50 pt-xl-50 folder-item"
                      >
                        <div
                          class="w-100 d-flex justify-content-between align-items-start"
                        >
                          <div class="flex-grow-1 pe-2">
                            <div class="d-flex align-items-center">
                              <p
                                class="m-0 font-weight-bolder one-line"
                                placement="bottom"
                                [openDelay]="300"
                                [ngbTooltip]="item.trich_yeu | stripHtml"
                              >
                                <span *ngIf="i < 10">0</span>{{ i + 1 }}.
                                <span class="text-primary"
                                  >{{ item.loai_van_ban }} -
                                </span>
                                {{ item.so_hieu }} -
                                <span [innerHTML]="item.trich_yeu"></span>
                              </p>
                              <span
                                appcopy
                                class="text-primary cursor-pointer ml-50"
                                [text]="
                                  item.loai_van_ban +
                                    ' - ' +
                                    item.so_hieu +
                                    ' - ' +
                                    item.trich_yeu | stripHtml
                                "
                                ngbTooltip="Sao chép"
                                container="body"
                                ><i data-feather="copy"></i
                              ></span>
                              <div
                                *ngIf="typeDocument == 'upload'"
                                (click)="
                                  deleteVanBanLienQuan(item);
                                  $event.stopPropagation()
                                "
                                class="mx-50"
                              >
                                <img
                                  ngbTooltip="Xoá"
                                  container="body"
                                  src="assets/images/icons/delete-clause.svg"
                                  alt="deleteClause"
                                  class="icon"
                                />
                              </div>
                            </div>

                            <div class="wrap-text mt-xxl-1 mt-xl-50">
                              <div class="d-flex flex-wrap gap-2">
                                <p *ngIf="item.tinh_trang_hieu_luc">
                                  Hiệu lực:
                                  <span
                                    class="font-weight-bolder"
                                    [ngClass]="{
                                      'text-danger':
                                        item.tinh_trang_hieu_luc ===
                                        'Hết hiệu lực toàn bộ',
                                      'text-success':
                                        item.tinh_trang_hieu_luc ===
                                        'Còn hiệu lực',
                                      'text-warning':
                                        item.tinh_trang_hieu_luc ===
                                        'Hết hiệu lực một phần',
                                      'text-primary':
                                        item.tinh_trang_hieu_luc ===
                                        'Chưa có hiệu lực',
                                      'text-secondary':
                                        item.tinh_trang_hieu_luc ===
                                        'Ngưng hiệu lực một phần',
                                      'text-muted':
                                        item.tinh_trang_hieu_luc ===
                                        'Không còn phù hợp'
                                    }"
                                  >
                                    {{ item.tinh_trang_hieu_luc }}
                                  </span>
                                </p>
                                <p *ngIf="item.ngay_ban_hanh">
                                  Ban hành:
                                  <span class="font-weight-bolder">
                                    {{
                                      item.ngay_ban_hanh
                                        | date : "dd-MM-yyyy" : "+0000"
                                    }}
                                  </span>
                                </p>
                                <p *ngIf="item.ngay_co_hieu_luc">
                                  Áp dụng:
                                  <span class="font-weight-bolder">
                                    {{
                                      item.ngay_co_hieu_luc
                                        | date : "dd-MM-yyyy" : "+0000"
                                    }}
                                  </span>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <hr />
                    </ng-container>
                  </div>
                  <ng-container *ngIf="listVanBanLienQuanFilter?.length == 0">
                    <div
                      class="d-flex flex-column justify-content-center align-items-center"
                    >
                      <img
                        src="assets/images/icons/no-file.svg"
                        alt="no-file"
                      />
                      <p class="font-weight-bolder h3 mt-2">
                        Không có thông tin
                      </p>
                    </div>
                  </ng-container>
                </div>
                <div *ngIf="isShowTable">
                  <div class="view-detail-file-table-container">
                    <ngx-datatable
                      #tableRowDetails
                      [rows]="listVanBanLienQuanFilter"
                      [rowHeight]="58"
                      class="bootstrap core-bootstrap cursor"
                      [columnMode]="ColumnMode.force"
                      [headerHeight]="40"
                      [footerHeight]="50"
                      [scrollbarH]="true"
                      [limit]="limit"
                      (activate)="onActivate($event)"
                      [count]="listVanBanLienQuanFilter.length"
                    >
                      <ngx-datatable-column
                        name="Tên văn bản"
                        prop="trich_yeu"
                        [width]="250"
                      >
                      </ngx-datatable-column>

                      <ngx-datatable-column
                        name="Loại văn bản"
                        prop="loai_van_ban"
                        [width]="150"
                      >
                      </ngx-datatable-column>
                      <ngx-datatable-column
                        name="Số hiệu"
                        prop="so_hieu"
                        [width]="150"
                      >
                      </ngx-datatable-column>

                      <ngx-datatable-column
                        name="Ngày ban hành"
                        prop="ngay_ban_hanh"
                        [width]="150"
                      >
                        <ng-template
                          let-status="value"
                          let-row="row"
                          ngx-datatable-cell-template
                        >
                          {{
                            row.ngay_ban_hanh | date : "dd-MM-yyyy" : "+0000"
                          }}
                        </ng-template>
                      </ngx-datatable-column>
                      <ngx-datatable-column
                        name="Ngày có hiệu lực"
                        prop="ngay_co_hieu_luc"
                        [width]="190"
                      >
                        <ng-template
                          let-value="value"
                          let-row="row"
                          ngx-datatable-cell-template
                        >
                          {{
                            row.ngay_co_hieu_luc | date : "dd-MM-yyyy" : "+0000"
                          }}
                        </ng-template>
                      </ngx-datatable-column>
                      <ngx-datatable-column
                        name="Trạng thái hiệu lực"
                        prop="tinh_trang_hieu_luc"
                        [width]="200"
                      >
                        <ng-template
                          let-status="value"
                          let-row="row"
                          ngx-datatable-cell-template
                        >
                          <div
                            [ngClass]="[
                              'badge',
                              status === 'Còn hiệu lực'
                                ? 'badge-light-primary'
                                : status === 'Hết hiệu lực một phần'
                                ? 'badge-light-warning'
                                : status === 'Hết hiệu lực toàn bộ'
                                ? 'badge-light-danger'
                                : 'badge-light-info'
                            ]"
                          >
                            {{ status }}
                          </div>
                        </ng-template>
                      </ngx-datatable-column>
                      <ngx-datatable-column
                        name="Hành động"
                        [width]="120"
                        [sortable]="false"
                      >
                        <ng-template ngx-datatable-cell-template let-row="row">
                          <span ngbDropdown container="body">
                            <a
                              (click)="deleteVanBanLienQuan(row)"
                              ngbDropdownToggle
                              class="hide-arrow text-primary"
                              data-toggle="dropdown"
                              aria-haspopup="true"
                              aria-expanded="false"
                            >
                              Xoá
                            </a>
                          </span>
                        </ng-template>
                      </ngx-datatable-column>
                    </ngx-datatable>
                  </div>
                </div>
              </ng-template>
            </li>
          </ul>
          <div [ngbNavOutlet]="nav"></div>
        </div>
      </div>
    </div>
  </div>
</div>
