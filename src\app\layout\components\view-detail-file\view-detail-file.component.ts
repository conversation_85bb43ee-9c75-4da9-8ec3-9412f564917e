import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  ElementRef,
  Input,
  NgZone,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from "@angular/core";
import { Dom<PERSON>anitizer, SafeHtml } from "@angular/platform-browser";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ColumnMode } from "@swimlane/ngx-datatable";
import { BoSungVanBanDieuKhoanService } from "app/main/apps/quan-ly-van-ban/detail-work-space/bo-sung-van-ban-dieu-khoan/bo-sung-van-ban-dieu-khoan.service";
import { ChatbotService } from "app/main/apps/quan-ly-van-ban/detail-work-space/chatbot/chatbot.service";
import { DetailWorkSpaceService } from "app/main/apps/quan-ly-van-ban/detail-work-space/detail-work-space.service";
import { ListDocumentService } from "app/main/apps/quan-ly-van-ban/detail-work-space/list-document/list-document.service";
import { TakeNoteService } from "app/main/apps/quan-ly-van-ban/detail-work-space/take-note/take-note.service";
import { FormType } from "app/models/FormType";
import { ShowContent } from "app/models/ShowContent";
import { ShowSideBar } from "app/models/ShowSideBa";
import { environment } from "environments/environment";
import { FlatpickrOptions } from "ng2-flatpickr";
import { NgxExtendedPdfViewerComponent } from "ngx-extended-pdf-viewer";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { take, takeUntil } from "rxjs/operators";
import Swal from "sweetalert2";
import { ViewDetailFileService } from "./view-detail-file.service";
@Component({
  selector: "app-view-detail-file",
  templateUrl: "./view-detail-file.component.html",
  styleUrls: ["./view-detail-file.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class ViewDetailFileComponent implements OnInit {
  @ViewChild("sumarizeContent")
  sumarizeContent!: ElementRef<HTMLParagraphElement>;
  @ViewChild("soHieuRef") soHieuRef!: ElementRef<HTMLTableCellElement>;
  @ViewChild("contentDiv") contentDiv!: ElementRef;
  @ViewChild(NgxExtendedPdfViewerComponent)
  pdfViewer: NgxExtendedPdfViewerComponent;
  @Input("modal") public modal: NgbActiveModal;
  @Input("type") public type: FormType;
  public isShowTable: boolean = false;
  public ColumnMode = ColumnMode;
  public sizePage = [10, 20, 30, 100];
  public limit: number = this.sizePage[0];
  public avtiveTab = "";
  public fileName = "";
  public fileInfor;
  public fileId: string = "";
  public dataFile: any;
  public parentRoute: string = "/";
  public _unSubscribe: Subject<any> = new Subject<any>();
  isLoading: boolean = true;
  public listVanBanCanCu: any[] = [];
  public listVanBanBiBaiBo: any[] = [];
  public listVanBanBiBaiBoMotPhan: any[] = [];
  public listVanBanBiDinhChi: any[] = [];
  public listVanBanBiDinhChiMotPhan: any[] = [];
  public listVanBanBiDinhChinh: any[] = [];
  public listVanBanBiHuyBo: any[] = [];
  public listVanBanBiHuyBoMotPhan: any[] = [];
  public listVanBanBiThayThe: any[] = [];
  public listVanBanBiThayTheMotPhan: any[] = [];
  public listVanBanChuaXacDinh: any[] = [];
  public listVanBanChuaXacDinhMotPhan: any[] = [];
  public listVanBanDanChieu: any[] = [];
  public listVanBanDuocHuongDan: any[] = [];
  public listVanBanQuyDinhChiTiet: any[] = [];
  public listVanBanDuocSuaDoi: any[] = [];
  public listVanBanDuocSuaDoiBoSung: any[] = [];
  public listVanBanLienQuanKhac: any[] = [];
  public listVanBanLienQuan: any[] = [];
  public listVanBanLienQuanFilter: any[] = [];
  public listVanBanBaiBo: any[] = [];
  public listVanBanBiHetHieuLuc: any[] = [];
  public listVanBanBiHetHieuLucMotPhan: any[] = [];
  public listVanBanDinhChi: any[] = [];
  public listVanBanDinhChiMotPhan: any[] = [];
  public listVanBanDuocQuyDinhChiTiet: any[] = [];
  public listVanBanHuongDan: any[] = [];
  public listVanBanHetHieuLuc: any[] = [];
  public listVanBanHetHieuLucMotPhan: any[] = [];
  public listVanBanHuyBo: any[] = [];
  public listVanBanHuyBoMotPhan: any[] = [];
  public listVanBanSuaDoi: any[] = [];
  public listVanBanSuaDoiBoSung: any[] = [];
  public listVanBanThayThe: any[] = [];
  public listVanBanThayTheMotPhan: any[] = [];
  public isToggleAddClause: boolean = false;
  public typeDocument: string;
  public listPanels = [
    { title: "Văn bản căn cứ", list: this.listVanBanCanCu },
    { title: "Văn bản bị bãi bỏ", list: this.listVanBanBiBaiBo },
    {
      title: "Văn bản bị bãi bỏ một phần",
      list: this.listVanBanBiBaiBoMotPhan,
    },
    { title: "Văn bản bị đình chỉ", list: this.listVanBanBiDinhChi },
    {
      title: "Văn bản bị đình chỉ một phần",
      list: this.listVanBanBiDinhChiMotPhan,
    },
    { title: "Văn bản bị đính chính", list: this.listVanBanBiDinhChinh },
    { title: "Văn bản bị huỷ bỏ", list: this.listVanBanBiHuyBo },
    {
      title: "Văn bản bị huỷ bỏ một phần",
      list: this.listVanBanBiHuyBoMotPhan,
    },
    { title: "Văn bản bị thay thế", list: this.listVanBanBiThayThe },
    {
      title: "Văn bản bị thay thế một phần",
      list: this.listVanBanBiThayTheMotPhan,
    },
    { title: "Văn bản chưa xác định", list: this.listVanBanChuaXacDinh },
    {
      title: "Văn bản chưa xác định một phần",
      list: this.listVanBanChuaXacDinhMotPhan,
    },
    { title: "Văn bản dẫn chiếu", list: this.listVanBanDanChieu },
    { title: "Văn bản được hướng dẫn", list: this.listVanBanDuocHuongDan },
    { title: "Văn bản quy định chi tiết", list: this.listVanBanQuyDinhChiTiet },
    { title: "Văn bản được sửa đổi", list: this.listVanBanDuocSuaDoi },
    {
      title: "Văn bản được sửa đổi bổ sung",
      list: this.listVanBanDuocSuaDoiBoSung,
    },
    { title: "Văn bản liên quan khác", list: this.listVanBanLienQuanKhac },

    { title: "Văn bản bãi bỏ", list: this.listVanBanBaiBo },
    { title: "Văn bản bị hết hiệu lực", list: this.listVanBanBiHetHieuLuc },
    {
      title: "Văn bản bị hết hiệu lực một phần",
      list: this.listVanBanBiHetHieuLucMotPhan,
    },
    { title: "Văn bản đình chỉ", list: this.listVanBanDinhChi },
    { title: "Văn bản đình chỉ một phần", list: this.listVanBanDinhChiMotPhan },
    {
      title: "Văn bản được quy định chi tiết",
      list: this.listVanBanDuocQuyDinhChiTiet,
    },
    { title: "Văn bản hướng dẫn", list: this.listVanBanHuongDan },
    { title: "Văn bản hết hiệu lực", list: this.listVanBanHetHieuLuc },
    {
      title: "Văn bản hết hiệu lực một phần",
      list: this.listVanBanHetHieuLucMotPhan,
    },
    { title: "Văn bản huỷ bỏ", list: this.listVanBanHuyBo },
    { title: "Văn bản huỷ bỏ một phần", list: this.listVanBanHuyBoMotPhan },
    { title: "Văn bản sửa đổi", list: this.listVanBanSuaDoi },
    { title: "Văn bản sửa đổi bổ sung", list: this.listVanBanSuaDoiBoSung },
    { title: "Văn bản thay thế một phần", list: this.listVanBanThayTheMotPhan },
  ];
  public FormType = FormType; // CREATE là truòng hợp xem văn bản từ mục chuyển đổi văn bản
  public listPanelsFiltered: any = [];
  public activePanelIds = [];
  public isEditTongQuan: boolean = false;
  safeHtml: SafeHtml;
  public statusSummarize;
  public luocdo: string;
  public isEditSumarize: boolean = false;
  public valueSumarizeEdited: string = "";
  public customDateOptions1: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
  };
  public customDateOptions2: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
  };
  public customDateOptions3: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
  };
  public listLoaiVanBan: any = [
    {
      label: "Quyết định",
      value: "Quyết định",
    },
    {
      label: "Thông tư",
      value: "Thông tư",
    },
    {
      label: "Nghị quyết",
      value: "Nghị quyết",
    },
    {
      label: "Nghị định",
      value: "Nghị định",
    },
    {
      label: "Thông tư liên tịch",
      value: "Thông tư liên tịch",
    },

    {
      label: "Luật",
      value: "Luật",
    },
    {
      label: "Văn bản hợp nhất",
      value: "Văn bản hợp nhất",
    },
    {
      label: "Pháp lệnh",
      value: "Pháp lệnh",
    },
    {
      label: "Công văn",
      value: "Công văn",
    },
    {
      label: "Bộ luật",
      value: "Bộ luật",
    },
    {
      label: "Nghị quyết liên tịch",
      value: "Nghị quyết liên tịch",
    },
    {
      label: "Chỉ thị",
      value: "Chỉ thị",
    },
    {
      label: "Văn bản khác",
      value: "Văn bản khác",
    },
    {
      label: "Lệnh",
      value: "Lệnh",
    },
    {
      label: "Hiến pháp",
      value: "Hiến pháp",
    },
    {
      label: "Văn bản liên quan",
      value: "Văn bản liên quan",
    },
    {
      label: "Thông báo",
      value: "Thông báo",
    },
    {
      label: "Chương trình",
      value: "Chương trình",
    },
    {
      label: "Sắc lệnh",
      value: "Sắc lệnh",
    },
    {
      label: "Thông tư liên bộ",
      value: "Thông tư liên bộ",
    },
    {
      label: "Hiệp định",
      value: "Hiệp định",
    },
    {
      label: "Sắc luật",
      value: "Sắc luật",
    },
  ];
  public isStreaming: boolean = false;
  public dataSummarize: string = "";
  public save: string = "true"; // để dùng cho trường hợp xem tài liệu được lưu từ tìm kiếm về, k hiển thị nút lưu
  public clauseId: string;
  public loadFileError: boolean = false;
  public streamingDone: boolean = false;
  public statusStreaming: number = 0;
  constructor(
    private route: ActivatedRoute,
    private viewDetailFile: ViewDetailFileService,
    private router: Router,
    private documentService: ListDocumentService,
    private toast: ToastrService,
    private sanitizer: DomSanitizer,
    private bosungVanBan: BoSungVanBanDieuKhoanService,
    private workspace: DetailWorkSpaceService,
    private chatbotService: ChatbotService,
    private ngZone: NgZone,
    private noteService: TakeNoteService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.queryParams
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((params) => {
        this.fileId = params["fileId"];
        this.avtiveTab = params["tabs"];
        this.typeDocument = params["type"];
        this.luocdo = params["luocdo"];
        this.save = params["save"];
        this.loadFileError = false;
        this.isLoading = true;
        this.isEditTongQuan = false;
        this.fileName = params["fileName"];
        this.dataSummarize = ""; // xoá data khi xem tài liệu mới, để load lại api tóm tắt văn bản
        this.isEditSumarize = false;
        if (this.dataFile?.docx_url) {
          this.dataFile.docx_url = null;
        }
        if (this.fileId) {
          if (params["type"] == "upload") {
            this.getDetailFile();
          } else {
            this.getDetailFileFromSearch();
          }
        }
        this.getLuocDo();
      });
    this.viewDetailFile.fileInfor
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        this.fileInfor = res;
      });

    this.viewDetailFile.clauseId // là id của điều từ chatbot
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        this.clauseId = res;
      });

    this.viewDetailFile.isSaveFileFromSearch
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        if (res) {
          this.getLuocDo();
        }
      });
  }
  ngAfterViewInit() {
    this.viewDetailFile.clauseId2 // là id của điều từ danh sáhc văn bản
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        console.log("res", res);

        this.clauseId = res;
        if (this.avtiveTab != "toanvan") {
          this.avtiveTab = "toanvan";
          this.cdr?.detectChanges(); // ép chạy CD ngay sau khi đổi tab
        }
        // 1) Chờ Angular stable
        this.ngZone.onStable.pipe(take(1)).subscribe(() => {
          // 2) Chờ browser paint ở frame kế tiếp rồi mới scroll
          requestAnimationFrame(() => {
            this.scrollToClause(res);
          });
        });
      });
    this.viewDetailFile.clauseId2 // là id của điều từ danh sáhc văn bản
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        console.log("res", res);

        this.clauseId = res;
        if (this.avtiveTab != "toanvan") {
          this.avtiveTab = "toanvan";
          this.cdr?.detectChanges(); // ép chạy CD ngay sau khi đổi tab
        }
        // 1) Chờ Angular stable
        this.ngZone.onStable.pipe(take(1)).subscribe(() => {
          // 2) Chờ browser paint ở frame kế tiếp rồi mới scroll
          requestAnimationFrame(() => {
            this.scrollToClause(res);
          });
        });
      });
    this.viewDetailFile.clauseId2 // là id của điều từ danh sáhc văn bản
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        console.log("res", res);

        this.clauseId = res;
        if (this.avtiveTab != "toanvan") {
          this.avtiveTab = "toanvan";
          this.cdr?.detectChanges(); // ép chạy CD ngay sau khi đổi tab
        }
        // 1) Chờ Angular stable
        this.ngZone.onStable.pipe(take(1)).subscribe(() => {
          // 2) Chờ browser paint ở frame kế tiếp rồi mới scroll
          requestAnimationFrame(() => {
            this.scrollToClause(res);
          });
        });
      });
  }
  transform(value: string): string {
    return value.replace(/<[^>]+>/g, ""); // Xóa tất cả thẻ HTML
  }

  saveFileToListDocument() {
    const workspaceId = this.route.snapshot.params.id;
    this.fileInfor = this.dataFile;
    if (this.fileInfor.ID) {
      this.fileInfor.id = this.fileInfor.ID;
      delete this.fileInfor.ID;
    }
    this.fileInfor.workspace_id = workspaceId; // Thêm workspace_id vào fileInfor
    if (typeof this.fileInfor.id === "string") {
      // check trong trường hợp đang xem văn bản khi tìm kiếm điều khoản, id của nó là dạng "xxxxdieu_1"
      this.fileInfor.id = this.fileInfor.doc_id;
    }

    this.viewDetailFile.addFileBySearch([this.fileInfor]).subscribe(
      (response) => {
        // console.log("Lưu thành công:", response);
        this.toast.success("Lưu tài liệu vào lịch sử tìm kiếm", "Thành công", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        this.workspace.isSaveFileFromSearch.next(true);
      },
      (error) => {
        // this.toast.error(error, "Thất bại", {
        //   closeButton: true,
        //   positionClass: "toast-top-right",
        //   toastClass: "toast ngx-toastr",
        // });
        // console.error("Lỗi khi lưu:", error);
      }
    );
  }
  scrollToTopContainer() {
    setTimeout(() => {
      const container = this.contentDiv?.nativeElement as HTMLElement;
      if (container) {
        container.scrollTop = 0; // ← Đẩy thanh cuộn về vị trí top
      }
    }, 0);
  }
  scrollToClause(clauseId: string) {
    setTimeout(() => {
      if (!this.contentDiv?.nativeElement) {
        return;
      }
      const container = this.contentDiv.nativeElement as HTMLElement;
      const target = container.querySelector(`#${clauseId}`) as HTMLElement;

      if (target) {
        target.scrollIntoView({ behavior: "smooth", block: "start" });
        target.classList.add("highlight-scroll");
        setTimeout(() => {
          target.classList.remove("highlight-scroll");
        }, 3000);
      } else {
        console.warn("Không tìm thấy clauseId trong DOM:", clauseId);
      }
    }, 0); // delay 300ms để chờ DOM render
  }

  public handleResponse(res: any): void {
    const relationTypeMapping: { [key: string]: string } = {
      van_ban_can_cu: "Văn bản căn cứ",
      van_ban_bi_bai_bo: "Văn bản bị bãi bỏ",
      van_ban_bi_bai_bo_mot_phan: "Văn bản bị bãi bỏ một phần",
      van_ban_bi_dinh_chi: "Văn bản bị đình chỉ",
      van_ban_bi_dinh_chi_mot_phan: "Văn bản bị đình chỉ một phần",
      van_ban_bi_dinh_chinh: "Văn bản bị đính chính",
      van_ban_bi_huy_bo: "Văn bản bị huỷ bỏ",
      van_ban_bi_huy_bo_mot_phan: "Văn bản bị huỷ bỏ một phần",
      van_ban_bi_thay_the: "Văn bản bị thay thế",
      van_ban_bi_thay_the_mot_phan: "Văn bản bị thay thế một phần",
      van_ban_chua_xac_dinh: "Văn bản chưa xác định",
      van_ban_chua_xac_dinh_mot_phan: "Văn bản chưa xác định một phần",
      van_ban_dan_chieu: "Văn bản dẫn chiếu",
      van_ban_duoc_huong_dan: "Văn bản được hướng dẫn",
      van_ban_quy_dinh_chi_tiet: "Văn bản quy định chi tiết",
      van_ban_duoc_sua_doi: "Văn bản được sửa đổi",
      van_ban_duoc_sua_doi_bo_sung: "Văn bản được sửa đổi bổ sung",
      van_ban_bai_bo: "Văn bản bị bãi bỏ",
      van_ban_bi_het_hieu_luc: "Văn bản bị hết hiệu lực",
      van_ban_bi_het_hieu_luc_mot_phan: "Văn bản bị hết hiệu lực một phần",
      van_ban_dinh_chi: "Văn bản đình chỉ",
      van_ban_dinh_chi_mot_phan: "Văn bản đình chỉ một phần",
      van_ban_duoc_quy_dinh_chi_tiet: "Văn bản được quy định chi tiết",
      van_ban_huong_dan: "Văn bản hướng dẫn",
      van_ban_het_hieu_luc: "Văn bản hết hiệu lực",
      van_ban_het_hieu_luc_mot_phan: "Văn bản hết hiệu lực một phần",
      van_ban_huy_bo: "Văn bản huỷ bỏ",
      van_ban_huy_bo_mot_phan: "Văn bản huỷ bỏ một phần",
      van_ban_sua_doi: "Văn bản sửa đổi",
      van_ban_sua_doi_bo_sung: "Văn bản sửa đổi bổ sung",
      van_ban_thay_the: "Văn bản thay thế",
      van_ban_thay_the_mot_phan: "Văn bản thay thế một phần",
    };

    this.listPanels = Object.keys(relationTypeMapping)
      .filter((key) => Array.isArray(res[key]) && res[key].length > 0)
      .map((key) => ({
        title: relationTypeMapping[key],
        list: res[key],
      }));
    this.listPanelsFiltered = this.listPanels;

    this.activePanelIds = this.listPanels.map((_, idx) => "ngb-panel-" + idx);
  }

  getDetailFile() {
    this.viewDetailFile
      .getDetailFile(this.fileId, this.typeDocument)
      .subscribe((res) => {
        this.dataFile = res;
        this.safeHtml = this.sanitizer.bypassSecurityTrustHtml(
          `${this.dataFile?.toan_van || ""}
  ` // để tránh angular tự xoá các id trong khi innerHTML đi vì lý do bảo mật
        );
        this.scrollToTopContainer();
        if (this.clauseId) {
          setTimeout(() => {
            this.scrollToClause(this.clauseId);
          }, 0); // Delay nhỏ để đảm bảo DOM cập nhật xong
        }
      });
  }
  getDetailFileFromSearch() {
    this.viewDetailFile
      .getDetailFileFromSearch(
        this.fileId,
        this.typeDocument,
        this.luocdo == "es" ? "1" : "0"
      )
      .subscribe((res) => {
        this.dataFile = res;
        this.safeHtml = this.sanitizer.bypassSecurityTrustHtml(
          // để tránh angular tự xoá các id trong khi innerHTML đi vì lý do bảo mật
          this.dataFile?.toan_van || ""
        );
        this.scrollToTopContainer();
        if (this.clauseId) {
          setTimeout(() => {
            this.scrollToClause(this.clauseId);
          }, 0); // Delay nhỏ để đảm bảo DOM cập nhật xong
        }
      });
  }
  contentLoaded() {
    setTimeout(() => {
      window.dispatchEvent(new Event("resize"));
    }, 300);
    this.isLoading = false;
  }
  getLuocDo() {
    this.viewDetailFile.getLuocDo(this.fileId).subscribe(
      (res) => {
        this.handleResponse(res);
        this.listVanBanLienQuan = res.van_ban_lien_quan_khac;
        this.listVanBanLienQuanFilter = res.van_ban_lien_quan_khac;
      },
      (error) => {
        this.listPanels = null;
        this.listPanelsFiltered = [];
      }
    );
  }
  changeNav(event) {
    const tabs = event.nextId;
    this.showPopover = false;
    this.avtiveTab = tabs;
    if (tabs === "luocdo") {
      // this.getLuocDo();
      this.isToggleAddClause = false;
      this.listPanelsFiltered = this.listPanels;
    }

    if (tabs === "lienquannoidung") {
      this.isToggleAddClause = false;
      this.listVanBanLienQuanFilter = this.listVanBanLienQuan;
    }
    if (tabs === "vanbangoc") {
      this.isLoading = true;
    }
    if (tabs === "tomtatvanban") {
      this.streamingDone = false;
      this.summarizeDocument();
    }
  }
  summarizeDocument() {
    this.isStreaming = true;
    this.viewDetailFile.summarizeDocument(this.fileId).subscribe(
      (res) => {
        this.isStreaming = false;
        if (res.results.length != 0) {
          this.statusSummarize = res.results[0].status;
          this.dataSummarize = res.results[0].summarize;
          this.statusStreaming = res.results[0].status;
        } else {
          this.dataSummarize = "Không có nội dung tóm tắt";
        }
      },
      (error) => {
        this.isStreaming = false;
        this.dataSummarize = "Không có dữ liệu";
        this.toast.error("Tóm tắt văn bản", "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    );
  }
  async streamSumarize() {
    this.dataSummarize = "";
    this.isStreaming = true;
    try {
      const response = await fetch(
        `${environment.apichatbot}/stream_summarize/${this.fileId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      if (!response.body) {
        console.error("❌ No response body");
        return;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";

      // this.checkDoneAnswer = false;
      while (true) {
        const { value, done } = await reader.read();
        if (done) {
          this.streamingDone = true;
          console.log("streaming done");
          this.toast.info("Quá trình tóm tắt đã xong", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.isStreaming = false;
          break;
        }
        buffer += decoder.decode(value, { stream: true });

        // console.log("json", buffer);
        const [jsonObjects, remaining] = this.extractJsonObjects(buffer);
        buffer = remaining;

        for (const obj of jsonObjects) {
          console.log("obj", obj);
          if (obj.status_code == 400) {
            this.dataSummarize = "Không có nội dung tóm tắt";
          } else {
            if (obj.text) {
              this.dataSummarize += obj.text;
            }
            if (obj.answer) {
              this.dataSummarize = obj.answer;
            }
          }
        }
      }
    } catch (error) {
      this.toast.error("Thất bại", "", {
        closeButton: true,
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
      });
    }
  }
  extractJsonObjects(str: string): any[] {
    const objects: any[] = [];
    let depth = 0;
    let start = -1;
    let inString = false;
    let escape = false;

    for (let i = 0; i < str.length; i++) {
      const char = str[i];

      if (char === '"' && !escape) {
        inString = !inString;
      }

      if (!inString) {
        if (char === "{") {
          if (depth === 0) start = i;
          depth++;
        } else if (char === "}") {
          depth--;
          if (depth === 0 && start !== -1) {
            const jsonStr = str.slice(start, i + 1);
            try {
              objects.push(JSON.parse(jsonStr));
            } catch (e) {
              console.warn("⚠️ JSON parse failed", jsonStr);
            }
            start = -1;
          }
        }
      }

      escape = char === "\\" && !escape;
    }

    const remaining = depth > 0 && start !== -1 ? str.slice(start) : "";
    return [objects, remaining];
  }
  saveSummarize(dataSummarize) {
    const body = {
      document: this.fileId,
      summarize: dataSummarize,
    };
    this.viewDetailFile.saveSumarizeDocument(body).subscribe(
      (res) => {
        this.dataSummarize = dataSummarize;
        this.toast.success("Lưu tóm tắt văn bản", "Thành công", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      },
      (error) => {
        this.toast.error("Lưu tóm tắt văn bản", "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    );
  }
  saveSummarizeEdited() {
    const body = {
      document: this.fileId,
      summarize: this.valueSumarizeEdited,
    };
    this.viewDetailFile.saveSumarizeDocument(body).subscribe(
      (res) => {
        this.toast.success("Lưu chỉnh sửa tóm tắt văn bản", "Thành công", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        this.isEditSumarize = false;
      },
      (error) => {
        // this.toast.error("Lưu tóm tắt văn bản", "Thất bại", {
        //   closeButton: true,
        //   positionClass: "toast-top-right",
        //   toastClass: "toast ngx-toastr",
        // });
      }
    );
  }
  cancelSummarizeEdited() {
    this.isEditSumarize = false;
  }
  goBackSearch() {
    this.documentService.goBack();
    this.documentService.FileSearchTemp.next("Xoá");
  }
  addVanBanCanCu() {
    this.isToggleAddClause = !this.isToggleAddClause;
    this.bosungVanBan.typeSearch.next(false);
    this.bosungVanBan.typeAddFile.next("Văn bản căn cứ");
    this.documentService.rightSideBarValue.next(
      this.isToggleAddClause ? ShowSideBar.AddClause : ShowSideBar.Note
    );
  }
  addVanBanLienQuan() {
    this.isToggleAddClause = !this.isToggleAddClause;
    this.bosungVanBan.typeSearch.next(false);
    this.bosungVanBan.typeAddFile.next("Văn bản liên quan");
    this.documentService.rightSideBarValue.next(
      this.isToggleAddClause ? ShowSideBar.AddClause : ShowSideBar.Note
    );
  }
  viewFileLienQuan(fileLienQuan) {
    // this.viewDetailFile.fileName.next(fileLienQuan.trich_yeu);
    this.documentService.setBehavior(ShowContent.Document);
    this.documentService.FileSearchTemp.next(fileLienQuan);
    this.viewDetailFile.clauseId2.next(null); // tránh khi bấm vào lược đồ lại bị cache scroll vào điều trước đó
    // this.viewDetailFile.fileInfor.next(fileLienQuan);

    this.router.navigate([], {
      queryParams: {
        fileId: fileLienQuan.id || fileLienQuan.ID,
        tabs: "toanvan",
        type: "search",
        luocdo: "es",
        fileName: fileLienQuan.trich_yeu,
        time: new Date().getTime(),
        save: true,
      },
      queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
    });
  }
  editTongQuan(soHieuRef, coQuanBanHanhRef, phamViRef) {
    this.isEditTongQuan = !this.isEditTongQuan;
    if (!this.isEditTongQuan) {
      // Lưu dữ liệu vào this.dataFile
      this.dataFile.so_hieu = soHieuRef.innerText.trim();
      this.dataFile.co_quan_ban_hanh = coQuanBanHanhRef.innerText.trim();
      this.dataFile.pham_vi = phamViRef.innerText.trim();

      // Chuẩn bị dữ liệu gửi đi
      const formData = new FormData();
      formData.append("so_hieu", this.dataFile.so_hieu);
      formData.append("loai_van_ban", this.dataFile.loai_van_ban);
      formData.append("co_quan_ban_hanh", this.dataFile.co_quan_ban_hanh);
      formData.append("pham_vi", this.dataFile.pham_vi);

      if (this.dataFile.ngay_ban_hanh)
        formData.append("ngay_ban_hanh", this.dataFile.ngay_ban_hanh);
      if (this.dataFile.ngay_co_hieu_luc)
        formData.append("ngay_co_hieu_luc", this.dataFile.ngay_co_hieu_luc);
      if (this.dataFile.ngay_dang_cong_bao)
        formData.append("ngay_dang_cong_bao", this.dataFile.ngay_dang_cong_bao);

      // Gửi API cập nhật
      this.viewDetailFile.editTongQuan(this.dataFile.id, formData).subscribe(
        (res) => {
          this.isEditTongQuan = false;
          this.toast.success("Cập nhật tổng quan", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        },
        (error) => {
          this.isEditTongQuan = true;
          this.toast.error("Cập nhật tổng quan", "Thất bại", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        }
      );
    } else {
      this.customDateOptions1.defaultDate = this.dataFile.ngay_ban_hanh;
      this.customDateOptions2.defaultDate = this.dataFile.ngay_co_hieu_luc;
      this.customDateOptions3.defaultDate = this.dataFile.ngay_dang_cong_bao;
      // Khi chuyển sang chế độ chỉnh sửa, focus vào input đầu tiên
      setTimeout(() => {
        this.soHieuRef.nativeElement.focus();
      });
    }
  }
  changeLoaiVanBan(event) {
    this.dataFile.loai_van_ban = event.value;
  }
  selectNgayBanHanh(event) {
    const date = event.target.value;
    this.dataFile.ngay_ban_hanh = date;
  }
  selectNgayCoHieuLuc(event) {
    const date = event.target.value;
    this.dataFile.ngay_co_hieu_luc = date;
  }
  selectNgayDangCongBao(event) {
    const date = event.target.value;
    this.dataFile.ngay_dang_cong_bao = date;
  }

  onActivate(event) {
    if (
      event.event.type === "click" &&
      event.column.name != "Hành động" &&
      event.column.name != "HÀNH ĐỘNG" &&
      event.column.name != "ACTION" &&
      event.column.name != "Action" &&
      event.column.name != "checkbox" &&
      event.column.name != ""
    ) {
      const fileLienQuan = event.row;
      this.documentService.setBehavior(ShowContent.Document);
      this.documentService.FileSearchTemp.next(fileLienQuan);
      this.viewDetailFile.clauseId2.next(null); // tránh khi bấm vào lược đồ lại bị cache scroll vào điều trước đó
      // this.viewDetailFile.fileInfor.next(fileLienQuan);

      this.router.navigate([], {
        queryParams: {
          fileId: fileLienQuan.id,
          tabs: "toanvan",
          type: "search",
          time: new Date().getTime(),
          fileName: fileLienQuan.trich_yeu,
          save: true,
        },
        queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
      });
    }
  }
  deleteVanBanLienQuan(file) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this.viewDetailFile.deleteRelateFile(file.id).subscribe((res) => {
          this.toast.success("Đã xoá văn bản liên quan", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.getLuocDo();
        });
      }
    });
  }

  filterLuocDO(event) {
    const searchValue = event.target.value
      .toLowerCase()
      .trim()
      .replace(/(\s)-|-(\s)/g, "$1$2") // xóa dấu '-' khi đứng cạnh khoảng trắng
      .replace(/\s+/g, " ");
    this.listPanelsFiltered = this.listPanels
      .map((panel) => {
        const filteredList = panel.list.filter((item) =>
          item.title?.toLowerCase().includes(searchValue)
        );

        return {
          ...panel,
          list: filteredList,
        };
      })
      .filter((panel) => panel.list.length > 0); // chỉ giữ lại các panel có list khớp
  }
  filterVanBanLienQuan(event) {
    const searchValue = event.target.value
      .toLowerCase()
      .trim()
      .replace(/(\s)-|-(\s)/g, "$1$2") // xóa dấu '-' khi đứng cạnh khoảng trắng
      .replace(/\s+/g, " ");
    console.log("searchValue", searchValue);

    this.listVanBanLienQuanFilter = this.listVanBanLienQuan.filter((item) =>
      item.title?.toLowerCase().includes(searchValue)
    );
  }

  private boldAdded = false;
  ngAfterViewChecked() {
    if (!this.boldAdded && this.contentDiv?.nativeElement) {
      this.addBoldToDieuElements();
      this.boldAdded = true; // gắn cờ để không thêm lại nhiều lần
    }
    // Khi bật edit thì focus
    if (this.isEditSumarize === true && this.sumarizeContent) {
      this.sumarizeContent.nativeElement.focus();
    }
  }

  addBoldToDieuElements() {
    if (this.contentDiv?.nativeElement) {
      const elements =
        this.contentDiv.nativeElement.querySelectorAll('[id^="dieu_"]');
      elements.forEach((el: HTMLElement) => {
        if (/^dieu_\d+$/.test(el.id)) {
          el.classList.add("font-weight-bold");
        }
      });
    }
  }

  showPopover = false;
  popoverPosition = { top: 0, left: 0 };
  selectedText = "";

  onTextSelect() {
    const contentElement = this.contentDiv.nativeElement;
    const selection = window.getSelection();
    const text = selection?.toString();

    if (text && selection?.rangeCount) {
      const range = selection.getRangeAt(0);
      const commonAncestor = range.commonAncestorContainer;
      console.log(contentElement.contains(commonAncestor));

      if (!contentElement.contains(commonAncestor)) {
        this.showPopover = false;
        return;
      }

      this.selectedText = text;
      // this.chatbotService.textBoiDen.next(text);

      const rects = range.getClientRects();
      if (rects.length === 0) {
        this.showPopover = false;
        return;
      }

      const lastRect = rects[rects.length - 1];
      const parentRect = contentElement.getBoundingClientRect();

      const popoverWidth = 200; // Giả sử chiều rộng popover
      const popoverHeight = 100; // Giả sử chiều cao popover
      const padding = 15;

      // === Tính vị trí ngang (left) như trước ===
      const spaceRight = parentRect.right - lastRect.right;
      const spaceLeft = lastRect.left - parentRect.left;

      let leftPosition;
      if (spaceRight >= popoverWidth + padding) {
        leftPosition = lastRect.right - parentRect.left + padding;
      } else if (spaceLeft >= popoverWidth + padding) {
        leftPosition = lastRect.left - parentRect.left - popoverWidth + 40;
      } else {
        leftPosition = Math.min(
          parentRect.width - popoverWidth - 10,
          lastRect.right - parentRect.left + padding
        );
      }

      // === Tính vị trí dọc (top hoặc lên trên nếu không đủ chỗ) ===
      const spaceBelow = parentRect.bottom - lastRect.bottom;
      const spaceAbove = lastRect.top - parentRect.top;

      let topPosition;
      if (spaceBelow >= popoverHeight + padding) {
        // đủ chỗ bên dưới
        topPosition = lastRect.bottom - parentRect.top + 5;
      } else if (spaceAbove >= popoverHeight + padding) {
        // đủ chỗ bên trên
        topPosition = lastRect.top - parentRect.top - popoverHeight - 5;
      } else {
        // không đủ chỗ cả trên lẫn dưới, ưu tiên bên dưới
        topPosition = Math.max(5, lastRect.bottom - parentRect.top + 5);
      }

      this.popoverPosition = {
        top: topPosition,
        left: leftPosition,
      };

      this.showPopover = true;
    } else {
      this.showPopover = false;
    }
  }

  copyText() {
    navigator.clipboard.writeText(this.selectedText);
    this.showPopover = false;
    this.toast.success("Sao chép", "Thành công", {
      closeButton: true,
      positionClass: "toast-top-right",
      toastClass: "toast ngx-toastr",
    });
  }
  askChatbot() {
    this.showPopover = false;
    this.workspace.isNotesCollapsed.next(false); // Mở sidebar ghi chú
    this.documentService.rightSideBarValue.next(ShowSideBar.Chatbot); // Mở sidebar ghi chú
    this.chatbotService.textFormVanBan.next(this.selectedText); // để gửi text được sao chép sang chatbot
    this.chatbotService.textBoiDen.next(null); // để bên chatbot k hiển thị text đã chọn
  }
  createNote() {
    this.showPopover = false;
    this.workspace.isNotesCollapsed.next(false); // Mở sidebar ghi chú
    this.documentService.rightSideBarValue.next(ShowSideBar.Note); // Mở sidebar ghi chú
    this.noteService.textFromVanBan.next(this.selectedText); // để gửi text được sao chép sang chatbot
  }
  onScrollContent() {
    this.showPopover = false;
  }
  highlightText() {
    // Gợi ý xử lý highlight: lưu vị trí, sửa innerHTML bằng cách wrap text được chọn
    alert("Chức năng highlight đang được phát triển");
    this.showPopover = false;
  }
  getFileExtension(url: string): string {
    if (!url) return "";
    const cleanUrl = url.split("?")[0];

    return cleanUrl.split(".").pop()?.toLowerCase() || "";
  }
  editSumarize(event: Event) {
    const target = event.target as HTMLElement;
    this.valueSumarizeEdited = target.innerText.trim(); // hoặc innerHTML nếu muốn giữ format
  }
  ngOnDestroy(): void {
    this._unSubscribe.next(true);
    this._unSubscribe.complete();
    this.viewDetailFile.clauseId.next(null);
    this.viewDetailFile.clauseId2.next(null);
    // this.viewDetailFile.fileName.next("");
  }

  cleanSummarize(text: string): string {
    if (!text) return '';
    
    // Gộp mọi \n liên tiếp thành 1
    return text.replace(/\n+/g, '\n')
               // nếu muốn loại bỏ \n giữa thẻ cũng được (tuỳ yêu cầu)
               .replace(/\n(<[^>]+>)/g, '$1')
               .replace(/(<\/[^>]+>)\n/g, '$1');
}



}
