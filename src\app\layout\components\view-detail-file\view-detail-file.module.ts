import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule, Routes } from "@angular/router";
import { CoreCommonModule } from "@core/common.module";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { Ng2FlatpickrModule } from "ng2-flatpickr";
import { NgxDocViewerModule } from "ngx-doc-viewer";
import { NgxExtendedPdfViewerModule } from "ngx-extended-pdf-viewer";
import { PipeModule } from "../pipe/pipe.module";
import { ViewDetailFileComponent } from "./view-detail-file.component";

const routes: Routes = [
  {
    path: "detail/:id",
    component: ViewDetailFileComponent,
  },
];
@NgModule({
  declarations: [ViewDetailFileComponent],
  imports: [
    CoreCommonModule,
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    FormsModule,
    NgbModule,
    NgxDatatableModule,
    PipeModule,
    Ng2FlatpickrModule,
    NgSelectModule,
    NgxDocViewerModule,
    NgxExtendedPdfViewerModule,
  ],
  exports: [ViewDetailFileComponent],
})
export class ViewDetailFileModule {}
