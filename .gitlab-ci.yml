before_script:
  - docker login -u $REGISTRY_IMAGE_USER -p $REGISTRY_IMAGE_TOKEN $CI_REGISTRY

workflow:
  rules:
    # - if: $CI_COMMIT_BRANCH == "main"
    #   variables:
    #     REGISTRY_IMAGE: registry.gitlab.com/ailabcati/prod/cls-web-frontend
    #     DOCKERFILE: Dockerfile
    # - if: $CI_COMMIT_BRANCH == "devhotfix"
    #   variables:
    #     REGISTRY_IMAGE: registry.gitlab.com/ailabcati/dev/cls-web-frontend
    #     DOCKERFILE: Dockerfile_dev

    - if: $CI_COMMIT_BRANCH == "mainquochoi"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/prod/cls-web-frontend
        DOCKERFILE: Dockerfile

    - if: $CI_COMMIT_BRANCH == "mainbtp"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/prod/clsbtp-web-frontend
        DOCKERFILE: Dockerfile

    - if: $CI_COMMIT_BRANCH == "devuiuxv3"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/dev/clsv3-web-frontend
        DOCKERFILE: Dockerfile_devui
    - if: $CI_COMMIT_BRANCH == "main_v2"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/prod/clsv3-web-frontend
        DOCKERFILE: Dockerfile_prodv2

build:
  stage: build
  script:
    - docker pull $REGISTRY_IMAGE:latest || true
    - docker build --cache-from $REGISTRY_IMAGE:latest --tag $REGISTRY_IMAGE:$CI_COMMIT_SHA --tag $REGISTRY_IMAGE:latest -f $DOCKERFILE .
    - docker push $REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker push $REGISTRY_IMAGE:latest
  only:
    - main_v2
    - develop
    - mainbtp
    - mainquochoi
    - devuiuxv3
    - devhotfix

deploy:
  stage: deploy
  # image: alpine:3.11
  image: cmcatilab/alpine:3.11_git_kustomize
  before_script:
    # - apk add --no-cache git curl bash
    # - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash
    # - mv kustomize /usr/local/bin/
    - git remote set-url origin https://$ARGOCD_REGISTRY_USER:$ARGOCD_REGISTRY_TOKEN@$ARGOCD_REPO
    - git clone https://$ARGOCD_REGISTRY_USER:$ARGOCD_REGISTRY_TOKEN@$ARGOCD_REPO
    - cd argo-config
  script:
    - git checkout -B main
    - cd $CONFIG_DIR
    - kustomize edit set image $REGISTRY_IMAGE:$CI_COMMIT_SHA
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Gitlab Runner"
    - git commit -am "CLS FE - $CI_COMMIT_BRANCH - $CI_COMMIT_MESSAGE"
    - git push origin main
  only:
    - main_v2
    - develop
    - mainbtp
    - mainquochoi
    - devuiuxv3
    - devhotfix
