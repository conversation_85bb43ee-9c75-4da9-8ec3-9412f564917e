<div
  class="p-0"
  [ngClass]="
    role == 'USER' ? 'container-fluid' : 'container-fluid container-lg'
  "
>
  <div class="card m-0">
    <div
      class="show-tour-guide w-100"
      *ngIf="listWorkSpaceFilter?.length == 0 && isNewUser"
    >
      Hướng dẫn các tính năng của CLS chỉ trong vài phút!

      <button
        class="btn btn-primary btn-sm ml-1"
        rippleEffect
        (click)="startTour()"
      >
        Hướng dẫn ngay
      </button>
    </div>
    <div class="card-body d-flex flex-column">
      <div class="d-flex cursor-pointer">
        <p
          [class.active]="!isShowDashboard"
          class="font-medium-5 font-weight-bolder mb-3 mr-1 menu-item"
          (click)="isShowDashboard = false"
        >
          Không gian làm việc
        </p>
      </div>
      <ng-container>
        <div class="row px-1 mb-1">
          <div
            class="col-12 mb-1 p-0 d-flex align-items-center justify-content-between"
          >
            <div class="d-flex align-items-center w-100">
              <button
                class="btn btn-primary mr-1 step-3"
                id="btn"
                rippleEffect
                (click)="addWorkSpace()"
              >
                <span [data-feather]="'plus'" [class]="'mr-25'"></span>
                <span class="d-none d-sm-inline">Thêm dự án</span>
              </button>
              <div class="form-group m-0 col-1 col-xl-6 p-0 position-relative">
                <input
                  type="text"
                  class="form-control ps-5 input-custom-1"
                  id="basicInput"
                  [placeholder]="isMobile ? '' : 'Tìm kiếm không gian làm việc'"
                  [formControl]="search"
                />

                <i
                  *ngIf="isMobile"
                  data-feather="search"
                  class="search-custom-1 d-xl-none"
                ></i>
              </div>
            </div>
            <div
              class="d-flex align-items-center flex-row w-100 justify-content-end"
            >
              <div class="d-flex align-items-center mr-50">
                <!-- Desktop / md+ -->
                <div
                  class="d-none step-1"
                  [ngClass]="role == 'USER' ? 'd-lg-flex' : 'd-xxl-flex'"
                >
                  <button
                    (click)="showSearchFile()"
                    type="button"
                    class="quan-ly-van-ban-action-button btn btn-outline-secondary mr-50"
                  >
                    <span data-feather="search" class="mr-50"></span>Tìm kiếm
                  </button>

                  <button
                    (click)="showConvertFile()"
                    type="button"
                    class="quan-ly-van-ban-action-button btn btn-outline-secondary mr-50"
                  >
                    <span data-feather="refresh-ccw" class="mr-50"></span>Chuyển
                    đổi
                  </button>

                  <button
                    (click)="showCompareFile()"
                    type="button"
                    class="quan-ly-van-ban-action-button btn btn-outline-secondary"
                  >
                    <img
                      src="assets/images/icons/compare-file.svg"
                      alt="compare"
                      class="quan-ly-van-ban-compare-icon mr-50"
                    />So sánh
                  </button>
                </div>

                <!-- Mobile / < lg -->
                <div
                  class="dropdown"
                  ngbDropdown
                  [ngClass]="role == 'USER' ? 'd-lg-none' : 'd-xxl-none'"
                >
                  <button
                    ngbDropdownToggle
                    class="btn btn-outline-secondary dropdown-toggle"
                    type="button"
                    id="actionsMenuBtn"
                    data-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    <span data-feather="menu"></span>
                  </button>
                  <div ngbDropdownMenu aria-labelledby="dropdownMenuButton">
                    <a
                      ngbDropdownItem
                      href="javascript:void(0)"
                      (click)="showSearchFile()"
                    >
                      <span data-feather="search" class="mr-50"></span>Tìm
                      kiếm</a
                    >
                    <a
                      ngbDropdownItem
                      href="javascript:void(0)"
                      (click)="showConvertFile()"
                    >
                      <span data-feather="refresh-ccw" class="mr-50"></span
                      >Chuyển đổi</a
                    >
                    <a
                      ngbDropdownItem
                      href="javascript:void(0)"
                      (click)="showCompareFile()"
                    >
                      <img
                        src="assets/images/icons/compare-file.svg"
                        alt="compare"
                        class="quan-ly-van-ban-compare-icon mr-50"
                      />So sánh</a
                    >
                  </div>
                </div>
              </div>

              <div class="d-flex align-items-center">
                <ng-select
                  (change)="sortProject($event)"
                  id="file1"
                  class="quan-ly-van-ban-sort-select w-100"
                  [items]="sortProjectList"
                  placeholder="Sắp xếp"
                  [clearable]="false"
                ></ng-select>

                <span
                  class="col-3 cursor-pointer pl-0"
                  (click)="loaiSapXep()"
                  [ngbTooltip]="typeSort ? 'Tăng dần' : 'Giảm dần'"
                  container="body"
                  placement="top"
                >
                  <img
                    src="assets/images/icons/desc.svg"
                    alt="desc"
                    *ngIf="typeSort"
                  />
                  <img
                    src="assets/images/icons/asc.svg"
                    alt="desc"
                    *ngIf="!typeSort"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="work-space mb-2">
          <div class="row">
            <ng-container *ngFor="let item of listWorkSpaceFilter">
              <div
                class="col-12 mb-1"
                [ngClass]="
                  role == 'USER'
                    ? 'col-xxl-2 col-xl-3 col-lg-4 col-sm-6'
                    : 'col-xxl-3 col-xl-4 col-lg-5 col-sm-6'
                "
                (click)="detailWorkSpace(item)"
              >
                <div class="workspace cursor-pointer" ngbDropdown>
                  <span class="d-flex justify-content-between mb-1">
                    <span>
                      <img src="assets/images/icons/folder.svg" alt="folder" />
                      <span class="ml-1">{{ item.doc_count }} tài liệu</span>
                    </span>
                    <img
                      (click)="$event.stopPropagation()"
                      src="assets/images/icons/3dots.svg"
                      alt="3dots"
                      ngbDropdownToggle
                    />
                  </span>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                    <button
                      class="w-100"
                      ngbDropdownItem
                      (click)="editWorkSpace(item); $event.stopPropagation()"
                    >
                      Sửa
                    </button>
                    <button
                      class="w-100"
                      ngbDropdownItem
                      (click)="deleteWorkSpace(item); $event.stopPropagation()"
                    >
                      Xoá
                    </button>
                  </div>
                  <div>
                    <p class="quan-ly-van-ban-workspace-title two-line">
                      {{ item.name }}
                    </p>
                    <ng-container *ngIf="item.created_at !== item.updated_at">
                      <p class="quan-ly-van-ban-workspace-date m-0">
                        Khởi tạo:
                        {{ item.created_at | date : "dd-MM-yyyy HH:mm:ss" }}
                      </p>
                      <p class="quan-ly-van-ban-workspace-date m-0">
                        Cập nhật:
                        {{ item.updated_at | date : "dd-MM-yyyy HH:mm:ss" }}
                      </p>
                    </ng-container>
                    <p
                      *ngIf="item.created_at === item.updated_at"
                      class="quan-ly-van-ban-workspace-date m-0"
                    >
                      Khởi tạo:
                      {{
                        item.created_at | date : "dd-MM-yyyy HH:mm:ss" : "+0000"
                      }}
                    </p>
                  </div>
                </div>
              </div>
            </ng-container>
            <ng-container *ngIf="listWorkSpaceFilter?.length == 0">
              <div class="no-workspace">
                <img src="assets/images/icons/no-file.svg" alt="no-file" />
                <p class="h3 mt-1">Không có không gian dự án nào</p>
              </div>
            </ng-container>
          </div>
        </div>
        <span
          class="w-100 d-flex justify-content-center mt-auto"
          *ngIf="totalWorkSpace > page_size"
        >
          <ngb-pagination
            [maxSize]="3"
            [collectionSize]="totalWorkSpace"
            [(page)]="currentPage"
            [(pageSize)]="page_size"
            aria-label="Default pagination"
            [rotate]="true"
            [ellipses]="false"
            [boundaryLinks]="true"
            (pageChange)="onPageChange($event)"
          >
          </ngb-pagination>
        </span>
        <button class="btn-chatbot chat-button" (click)="showChatbot()">
          <div class="chat-container">
            <img
              src="assets/images/icons/chatbot-message.svg"
              alt="chatbot"
              class="chat-icon"
            />
            <div class="dots">
              <span class="dot"></span>
              <span class="dot"></span>
              <span class="dot"></span>
            </div>
          </div>
        </button>
      </ng-container>
    </div>
  </div>
</div>
<ng-template #editDocumentNameModal let-modal>
  <div class="modal-body">
    <div class="col-12 p-0">
      <div class="form-group">
        <label
          for="basicTextarea"
          class="w-100 align-items-center d-flex justify-content-between"
          >Chỉnh sửa tên tài liệu
          <div class="">
            <button
              class="btn btn-sm ml-auto p-0"
              (click)="modal.dismiss('Cross click')"
            >
              <img src="assets/images/icons/x.svg" alt="x" />
            </button></div
        ></label>
      </div>
    </div>
    <div class="col-12 p-0">
      <form #editNameForm="ngForm" (ngSubmit)="updateName(modal)">
        <div class="form-group">
          <label for="documentName">Tên tài liệu</label>
          <input
            type="text"
            id="documentName"
            class="form-control"
            [formControl]="fileName"
            name="documentName"
            required
            maxlength="50"
          />
        </div>

        <div class="modal-footer">
          <button type="submit" class="btn btn-primary">Lưu</button>
          <button
            type="button"
            class="btn btn-secondary"
            (click)="modal.dismiss()"
          >
            Hủy
          </button>
        </div>
      </form>
    </div>
  </div>
</ng-template>
<ng-template #modalWorkSpace let-modal>
  <app-work-space-control
    (destroyed)="onChildDestroyed()"
    [title]="title"
    [modal]="modal"
    [type]="type"
    [row]="row"
  ></app-work-space-control>
</ng-template>
<ng-template #modalConvertFile let-modal>
  <app-boc-tach-thong-tin></app-boc-tach-thong-tin>
</ng-template>
<ng-template #modalSearchFile let-modal>
  <app-tim-kiem-ai [modal]="modal" [type]="type"></app-tim-kiem-ai>
</ng-template>
<ng-template #modalChatbot let-modal>
  <app-chatbot [modal]="modal" [type]="type"></app-chatbot>
</ng-template>
<ng-template #modalCompareFile let-modal>
  <app-so-sanh-van-ban [modal]="modal" [type]="type"></app-so-sanh-van-ban>
</ng-template>
