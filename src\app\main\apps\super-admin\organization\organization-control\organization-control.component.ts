import { Component, Input, OnInit, ViewEncapsulation } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AuthenticationService } from "app/auth/service";
import { FormType } from "app/models/FormType";
import { ToastrService } from "ngx-toastr";
import { OrganizationService } from "../organization.service";
import { emailValidator } from "app/shared/EmailValidator";

@Component({
  selector: "app-organization-control",
  templateUrl: "./organization-control.component.html",
  styleUrls: ["./organization-control.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class OrganizationControlComponent implements OnInit {
  @Input("modal") public modal: NgbActiveModal;
  @Input("title") public title: string;
  @Input("type") public type: FormType;
  @Input("data") public data: any;
  @Input("listOrganization") public listOrganization: any;
  public breadcrumbDefault: object;
  public organizationForm: FormGroup;
  public FormType = FormType;
  public statusOrgan = [
    { value: "active", label: "Hoạt động" },
    { value: "inactive", label: "Không hoạt động" },
  ];
  public submitted: boolean = false;
  public role: string;
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private _organService: OrganizationService,
    private _authenService: AuthenticationService,
    private _toast: ToastrService
  ) {
    this.organizationForm = this.fb.group({
      name: [null, [Validators.required, Validators.maxLength(255)]],
      description: [null, Validators.maxLength(255)],
      represent_people: [null, Validators.maxLength(255)],
      email: [null, [Validators.maxLength(255), emailValidator]],
      phone: [
        null,
        [Validators.pattern(/^0\d{9}$/), Validators.maxLength(255)],
      ],
      address: [null, Validators.maxLength(255)],
      parent_organization: [null],
    });
    this.role = _authenService.currentUserValue.role;
    if (this.role == "ADMIN")
      this.organizationForm.patchValue({
        parent_organization: localStorage.getItem("organization_id"),
      });
  }

  ngOnInit(): void {
    if (this.type == FormType.Update) {
      this.organizationForm.patchValue({ ...this.data });
    }
  }
  get f() {
    return this.organizationForm.controls;
  }
  submitSaveOrganization() {
    this.submitted = true;
    if (this.organizationForm.invalid) {
      return;
    }
    const formData = new FormData();
    for (const key in this.organizationForm.value) {
      if (this.organizationForm.value.hasOwnProperty(key)) {
        const value = this.organizationForm.value[key];
        formData.append(key, value == null ? '' : value);
      }
    }

    if (this.type == FormType.Create) {
      this._organService.addOrganization(formData).subscribe(
        (res) => {
          this.modal.close();
          this._organService.reloadData.next(true);
          this._toast.success(
            this.role == "SUPER_ADMIN" ? "Thêm tổ chức" : "Thêm phòng ban",
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
        },
        (error) => {
          this._toast.error(
            this.role == "SUPER_ADMIN"
              ? "Tổ chức đã tồn tại"
              : "Phòng ban đã tồn tại",
            "Thất bại",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
        }
      );
    } else {
      this._organService.updateOrganization(formData, this.data.id).subscribe(
        (res) => {
          this.modal.close();
          this._organService.reloadData.next(true);
          this._toast.success(
            this.role == "SUPER_ADMIN"
              ? "Cập nhật tổ chức"
              : "Cập nhật phòng ban",
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
        },
        (error) => {
          this._toast.error(
            this.role == "SUPER_ADMIN"
              ? "Tổ chức đã tồn tại"
              : "Phòng ban đã tồn tại",
            "Thất bại",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
        }
      );
    }
  }

  onCancel() {
    this.router.navigate(["super-admin/organization"]);
  }
}
