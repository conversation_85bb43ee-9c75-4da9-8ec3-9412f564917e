<div class="modal-body" tabindex="0" ngbAutofocus>
  <div class="row">
    <div class="col-12">
      <div class="form-group">
        <label for="basicTextarea" class="w-100 align-items-center d-flex justify-content-between">
          <h4>Thông tin người dùng</h4>
          <button class="btn btn-sm ml-auto p-0" (click)="modal.dismiss('Cross click')">
            <img src="assets/images/icons/x.svg" alt="x" />
          </button>
        </label>
      </div>
    </div>
    <div class="col-12">
      <form
        [formGroup]="userForm"
        class="d-flex flex-column justify-content-center"
      >
        <div class="avatar user-profile-avatar">
          <img
            class="user-profile-avatar-image"
            [src]="avatarUrl"
            alt="avatar"
            ngbTooltip="Click để thay đổi ảnh đại diện"
            (click)="fileInput.click(); fileInput.value = ''"
          />
          <input
            #fileInput
            type="file"
            class="user-profile-avatar-input"
            id="avatarInput"
            accept=".jpeg, .jpg, .png"
            (change)="onAvatarSelected($event)"
          />
        </div>
        <div class="mb-1">
          <label class="form-label" for="fullname">
            Tên hiển thị <span class="text-danger">*</span>
          </label>
          <input
            type="text"
            class="form-control"
            id="fullname"
            formControlName="fullname"
            placeholder="Nhập"
            appFormControlValidation
          />
        </div>

        <div class="mb-1">
          <label class="form-label" for="ngaySinh">Ngày sinh</label>
          <input
            type="text"
            class="form-control"
            id="ngaySinh"
            formControlName="dob"
            placeholder="YYYY-MM-DD"
            maxlength="10"
            (input)="onDobInput($event)"
            appFormControlValidation="Định dạng ngày sinh phải là YYYY-MM-DD"
            autocomplete="off"
          />
        </div>
        <div class="">
          <label class="form-label">Giới tính</label>
          <fieldset class="form-group">
            <ng-select
              formControlName="gender"
              [items]="gender"
              [clearable]="false"
              placeholder="Chọn"
              appFormControlValidation
            ></ng-select>
          </fieldset>
        </div>
        <div class="mb-1">
          <label class="form-label" for="address">Địa chỉ</label>
          <input
            type="text"
            placeholder="Nhập"
            class="form-control"
            id="address"
            formControlName="address"
            appFormControlValidation
          />
        </div>

        <div class="mb-1">
          <label class="form-label" for="phone">Số điện thoại</label>
          <input
            type="text"
            placeholder="Nhập"
            class="form-control"
            id="phone"
            formControlName="phone"
            appFormControlValidation="Số điện thoại bắt đầu bằng số 0 và có 10 chữ số"
          />
        </div>
      </form>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button
    rippleEffect
    class="btn btn-secondary mr-1"
    (click)="modal.close('Cross click')"
  >
    Huỷ
  </button>
  <button rippleEffect class="btn btn-primary mr-1" (click)="submitSaveUser()">
    Lưu
  </button>
</div>
