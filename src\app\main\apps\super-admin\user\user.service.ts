import { Injectable } from "@angular/core";
import { ApiService } from "../../../../../../util/ServiceApi";
import { HttpClient } from "@angular/common/http";
import { environment } from "environments/environment";
import { BehaviorSubject } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class UserService extends ApiService {
  public data: BehaviorSubject<any> = new BehaviorSubject(null);
  constructor(private http: HttpClient) {
    super(http, "user");
  }
  createUser(body) {
    return this.http.post(`${environment.apiUrl}/auth/create-user`, body);
  }
  getPosition(idOrgan) {
    return this.http.get<any>(
      `${environment.apiUrl}/position?organization=${idOrgan}`
    );
  }
  resetPassword(idUser, new_password) {
    const formData = new FormData();
    formData.append("user_id", idUser);
    if (new_password) {
      formData.append("new_password", new_password);
    }
    return this.http.post(
      `${environment.apiUrl}/auth/admin-reset-password`,
      formData
    );
  }
  activateUser(idUser) {
    return this.http.post(`${environment.apiUrl}/user/${idUser}/active/`, {});
  }
  deActivateUser(idUser) {
    return this.http.post(`${environment.apiUrl}/user/${idUser}/deactive/`, {});
  }
  updateStatus(idUser, body) {
    return this.http.post(
      `${environment.apiUrl}/user/${idUser}/update_active_status/`,
      body
    );
  }
}
