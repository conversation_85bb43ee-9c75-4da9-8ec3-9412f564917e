import { Component, Input, OnInit, ViewEncapsulation } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AuthenticationService } from "app/auth/service";
import { FlatpickrOptions } from "ng2-flatpickr";
import { ToastrService } from "ngx-toastr";
import { FormatTime } from "../../../../util/formatTime";
import { noOnlySpacesValidator } from "../../../../util/noSpaceValidator";
import { UserProfileService } from "./user-profile.service";

@Component({
  selector: "app-user-profile",
  templateUrl: "./user-profile.component.html",
  styleUrls: ["./user-profile.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class UserProfileComponent implements OnInit {
  @Input("modal") public modal: NgbActiveModal;
  public userForm: FormGroup;
  public customDateOptions: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
    mode: "single",
    defaultDate: undefined,
    // maxDate: "today",
  };
  public avatarUrl: string = "assets/images/portrait/small/avatar-s-1.jpg";
  public formData;
  public gender = ["Nam", "Nữ"];
  public avatarFile: any;
  constructor(
    private modalService: NgbModal,
    private fb: FormBuilder,
    private authService: AuthenticationService,
    private userProfileService: UserProfileService,
    private toastSerive: ToastrService
  ) {}

  createUserForm() {
    this.userForm = this.fb.group({
      avatar: [null],
      fullname: [null, [Validators.required, noOnlySpacesValidator, Validators.maxLength(255)]],
      dob: [null],
      gender: [null],
      address: [null, [noOnlySpacesValidator, Validators.maxLength(255)]],
      phone: [null, Validators.pattern(/^0\d{9}$/)],
    });
    const userInfor = JSON.parse(localStorage.getItem("current_User"));

    this.userForm.patchValue({
      // fullname: this.authService.currentUserValue.fullname,
      ...userInfor,
    });
    this.avatarUrl = this.authService.currentUserValue.avatar
      ? this.authService.currentUserValue.avatar
      : "assets/images/portrait/small/users.png";
    this.customDateOptions.defaultDate = new Date(userInfor.dob);
  }

  ngOnInit(): void {
    this.createUserForm();
  }

  onDobInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/[^0-9\-]/g, ""); // chỉ cho phép số và dấu '-'
    // Tự động thêm dấu '-' sau năm (YYYY) và tháng (MM)
    if (value.length === 4 && !value.includes('-')) {
      value = value + '-';
    } else if (value.length === 7 && value.split('-').length === 2) {
      value = value + '-';
    }
    // Giới hạn độ dài tối đa 10 ký tự (YYYY-MM-DD)
    value = value.slice(0, 10);
    input.value = value;
    this.userForm.get('dob')?.setValue(value, { emitEvent: false });
  }

  onAvatarSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];
      const maxSizeMB = 5;
      const maxSizeBytes = maxSizeMB * 1024 * 1024;
      if (file.size > maxSizeBytes) {
        this.toastSerive.error("Vui lòng chọn ảnh dưới 5MB!", "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        input.value = ""; // Reset file input
        return;
      }
      this.avatarFile = file;
      const reader = new FileReader();

      reader.onload = () => {
        this.avatarUrl = reader.result as string;
        this.userForm.patchValue({ avatar: this.avatarUrl });
      };

      reader.readAsDataURL(file);
    }
  }
  changeNgayBanHanh(event) {
    console.log(event.target.value);
  }

  submitSaveUser() {
    if (this.userForm.invalid) {
      this.userForm.markAllAsTouched();
      return;
    }

    this.formData = new FormData();
    
    if (this.avatarFile) {
      this.formData.append("avatar", this.avatarFile);
    }

    this.formData.append("fullname", this.userForm.get("fullname")?.value);
    this.formData.append("dob", this.userForm.get("dob")?.value);
    this.formData.append("gender", this.userForm.get("gender")?.value);
    this.formData.append("phone", this.userForm.get("phone")?.value);
    this.formData.append("address", this.userForm.get("address")?.value);

    this.userProfileService.updateProfile(this.formData).subscribe(
      (res: any) => {
        this.toastSerive.success("Cập nhật thông tin!", "Thành công", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        this.authService.currentUserSubject.next(res.data);
        localStorage.setItem("current_User", JSON.stringify(res.data));
        this.formData = new FormData();
        this.modalService.dismissAll();
      },
      (error) => {
        this.toastSerive.error("Cập nhật thông tin!", "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    );
  }
}
