<div
  class="d-flex p-0"
  [ngClass]="{
    container: isUrlAdmin,
    'dashboard-controls-admin': isUrlAdmin,
    'dashboard-controls-user': !isUrlAdmin
  }"
>
  <ng-select
    *ngIf="role == 'SUPER_ADMIN'"
    [items]="listOrganization"
    bindLabel="name"
    bindValue="id"
    placeholder="Tất cả tổ chức"
    class="dashboard-organization-select mr-1"
    (change)="changeOrganization($event)"
  >
  </ng-select>
  <ng2-flatpickr
    [ngClass]="'firstsecond'"
    [config]="basicDateOptions"
    name="basicDate"
    (change)="onChangDate($event)"
    class="dashboard-date-picker mr-1"
  ></ng2-flatpickr>

  <ng-select
    [items]="listDate"
    bindLabel="name"
    bindValue="id"
    placeholder="Chọn ngày"
    class="dashboard-date-select mr-0"
    (change)="changeDate($event)"
  ></ng-select>
</div>
<div class="p-0" [ngClass]="isUrlAdmin ? 'container' : 'container-fluid'">
  <div class="card-body statistics-body p-0">
    <div class="row">
      <div class="col-sm-6 col-xl-4 mb-2">
        <div class="statistics-card">
          <div class="media">
            <div class="avatar bg-light-primary mr-2">
              <div class="avatar-content">
                <i data-feather="users" class="avatar-icon"></i>
              </div>
            </div>
            <div class="media-body my-auto">
              <h4 class="font-weight-bolder font-xl mb-0 font-xl">
                {{ dataDashboard?.total_users | number }}
              </h4>
              <p class="card-text font-small-3 mb-0">
                Tổng số tài khoản đang hoạt động
              </p>
            </div>
            <div
              class="ml-1"
              ngbTooltip="Số lượng tài khoản đang hoạt động trên hệ thống tại thời điểm hiện tại."
            >
              <i
                data-feather="help-circle"
                class="font-medium-3 cursor-pointer"
              ></i>
            </div>
          </div>
        </div>
      </div>

      <div class="col-sm-6 col-xl-4 mb-2">
        <div class="statistics-card">
          <div class="media">
            <div class="avatar bg-light-warning mr-2">
              <div class="avatar-content">
                <i data-feather="user-plus" class="avatar-icon"></i>
              </div>
            </div>
            <div class="media-body my-auto">
              <h4 class="font-weight-bolder font-xl mb-0">
                {{ dataDashboard?.new_users | number }}
              </h4>
              <p class="card-text font-small-3 mb-0">Tài khoản mới</p>
            </div>
            <div>
              <img
                [src]="
                  dataDashboard?.change_type == 'increase'
                    ? 'assets/images/icons/crease.svg'
                    : dataDashboard?.change_type == 'no_change'
                    ? 'assets/images/icons/no-change.svg'
                    : 'assets/images/icons/decrease.svg'
                "
                alt="crease.svg"
              />
              <p
                [ngClass]="
                  dataDashboard?.change_type == 'increase'
                    ? 'text-success'
                    : dataDashboard?.change_type == 'no_change'
                    ? 'text-muted'
                    : 'text-danger'
                "
              >
                {{ dataDashboard?.change_amount | number }} người dùng
              </p>
            </div>
            <div
              class="ml-1"
              ngbTooltip="Số tài khoản được tạo mới trong khoảng thời gian đã chọn."
            >
              <i
                data-feather="help-circle"
                class="font-medium-3 cursor-pointer"
              ></i>
            </div>
          </div>
        </div>
      </div>

      <div class="col-sm-6 col-xl-4 mb-2">
        <div class="statistics-card">
          <div class="media">
            <div class="avatar bg-light-danger mr-2">
              <div class="avatar-content">
                <i data-feather="database" class="avatar-icon"></i>
              </div>
            </div>
            <div class="media-body my-auto">
              <h4 class="font-weight-bolder font-xl mb-0">
                {{ dataDashboard?.total_requests | number }}
              </h4>
              <p class="card-text font-small-3 mb-0">Tổng lượt sử dụng</p>
            </div>
            <div>
              <img
                [src]="
                  dataDashboard?.change_type_requests == 'increase'
                    ? 'assets/images/icons/crease.svg'
                    : dataDashboard?.change_type_requests == 'no_change'
                    ? 'assets/images/icons/no-change.svg'
                    : 'assets/images/icons/decrease.svg'
                "
                alt="crease.svg"
              />
              <p
                [ngClass]="
                  dataDashboard?.change_type_requests == 'increase'
                    ? 'text-success'
                    : dataDashboard?.change_type_requests == 'no_change'
                    ? 'text-muted'
                    : 'text-danger'
                "
              >
                {{ dataDashboard?.change_amount_requests | number : "1.0-0" }}
                lượt
              </p>
            </div>
            <div
              class="ml-1"
              ngbTooltip="Tổng số lần người dùng sử dụng hệ thống trong khoảng thời gian đã chọn."
            >
              <i
                data-feather="help-circle"
                class="font-medium-3 cursor-pointer"
              ></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <section>
    <div class="row">
      <div
        class="col-lg-4 col-12"
        *ngIf="!isChooseOrgan && role == 'SUPER_ADMIN'"
      >
        <div class="dashboard-card card h-100">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h4 class="card-title">Tỷ trọng người dùng theo tổ chức</h4>
            <span
              ngbTooltip="Tỉ lệ phân bổ người dùng theo từng tổ chức đang hoạt động trên hệ thống tính tới thời điểm hiện tại."
            >
              <i
                data-feather="help-circle"
                class="font-medium-3 cursor-pointer"
              ></i>
            </span>
          </div>
          <div class="card-body">
            <div id="donut-chart" #apexDonutChartRef>
              <apx-chart
                [series]="apexDonutChart.series"
                [chart]="{
                  height: 500,
                  width:
                    isMenuToggled === false
                      ? apexDonutChartRef.offsetWidth
                      : apexDonutChart.chart.width,
                  type: 'donut'
                }"
                [labels]="apexDonutChart.labels"
                [plotOptions]="apexDonutChart.plotOptions"
                [responsive]="apexDonutChart.responsive"
                [colors]="apexDonutChart.colors"
                [legend]="apexDonutChart.legend"
              ></apx-chart>
            </div>
          </div>
        </div>
      </div>
      <div
        class="col-12"
        [ngClass]="isChooseOrgan || role == 'ADMIN' ? 'col-lg-12' : 'col-lg-8'"
      >
        <div class="dashboard-card card h-100">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h4 class="card-title">Người dùng hoạt động hằng ngày</h4>
            <div class="cursor-pointer">
              <span
                ngbTooltip="Số người dùng có hoạt động trong từng ngày trong khoảng thời gian đã chọn."
              >
                <i
                  data-feather="help-circle"
                  class="font-medium-3 cursor-pointer"
                ></i>
              </span>
              <span
                class="ml-50"
                (click)="exportUserActivity()"
                ngbTooltip="Xuất tài liệu báo cáo người dùng hoạt động hàng ngày."
              >
                <img src="assets/images/icons/export2.svg" alt="export" />
              </span>
            </div>
          </div>
          <div class="card-body">
            <div id="chartjs-bar-chart">
              <canvas
                baseChart
                #barChartRef
                height="500"
                [datasets]="chartUserActivity.datasets"
                [labels]="chartUserActivity.labels"
                [options]="chartUserActivity.options"
                [legend]="chartUserActivity.legend"
                [chartType]="chartUserActivity.chartType"
                (chartClick)="onChartClick($event)"
              >
              </canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section>
    <div class="row mt-2">
      <div class="col-lg-6 col-12">
        <div class="dashboard-card card">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h4 class="card-title">Người dùng mới hằng ngày</h4>
            <div class="cursor-pointer">
              <span
                ngbTooltip="Số lượng người dùng được thêm mới theo từng ngày trong khoảng thời gian đã chọn."
              >
                <i
                  data-feather="help-circle"
                  class="font-medium-3 cursor-pointer"
                ></i>
              </span>
              <span
                class="ml-50"
                (click)="exportNewUserActivity()"
                ngbTooltip="Xuất tài liệu báo cáo người dùng mới hàng ngày."
              >
                <img src="assets/images/icons/export2.svg" alt="export" />
              </span>
            </div>
          </div>
          <div class="dashboard-chart-body card-body">
            <canvas
              class="line-chart-ex chartjs"
              baseChart
              height="400"
              [datasets]="lineChartNewUser.datasets"
              [chartType]="lineChartNewUser.chartType"
              [options]="lineChartNewUser.options"
              [labels]="lineChartNewUser.labels"
              [plugins]="plugins"
              (chartClick)="onChartClickNewUser($event)"
            ></canvas>
          </div>
        </div>
      </div>
      <div class="col-lg-6 col-12">
        <div class="dashboard-card card">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h4 class="card-title">Chi tiết lượt sử dụng theo tính năng</h4>
            <span
              ngbTooltip="Số lượt sử dụng của người dùng được phân theo loại tính năng và từng ngày trong khoảng thời gian đã chọn."
            >
              <i
                data-feather="help-circle"
                class="font-medium-3 cursor-pointer"
              ></i>
            </span>
          </div>
          <div class="dashboard-chart-body card-body">
            <canvas
              class="line-chart-ex chartjs"
              baseChart
              height="400"
              [datasets]="lineChartDetailConversation2?.datasets"
              [chartType]="lineChartDetailConversation2?.chartType"
              [options]="lineChartDetailConversation2?.options"
              [labels]="lineChartDetailConversation2?.labels"
              [plugins]="plugins"
            ></canvas>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="dashboard-card card">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h4 class="card-title">Chi tiết lượt sử dụng theo tính năng</h4>
            <span
              ngbTooltip="Số lượt sử dụng của người dùng được phân theo loại tính năng và từng ngày trong khoảng thời gian đã chọn."
            >
              <i
                data-feather="help-circle"
                class="font-medium-3 cursor-pointer"
              ></i>
            </span>
          </div>
          <div class="dashboard-chart-body card-body">
            <canvas
              class="line-chart-ex chartjs"
              baseChart
              height="400"
              [datasets]="lineChartDetailConversation?.datasets"
              [chartType]="lineChartDetailConversation?.chartType"
              [options]="lineChartDetailConversation?.options"
              [labels]="lineChartDetailConversation?.labels"
              [plugins]="plugins"
            ></canvas>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="dashboard-card card">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h4 class="card-title">Tổng lượt sử dụng hằng ngày</h4>
            <span
              ngbTooltip="Tổng số lượt sử dụng của người dùng theo từng ngày trong khoảng thời gian đã chọn."
            >
              <i
                data-feather="help-circle"
                class="font-medium-3 cursor-pointer"
              ></i>
            </span>
          </div>
          <div class="dashboard-chart-body card-body">
            <canvas
              class="line-chart-ex chartjs"
              baseChart
              height="400"
              [datasets]="lineChartSumConversation?.datasets"
              [chartType]="lineChartSumConversation?.chartType"
              [options]="lineChartSumConversation?.options"
              [labels]="lineChartSumConversation?.labels"
              [plugins]="plugins"
            ></canvas>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
<ng-template #modalDetailUserActive let-modal>
  <div class="modal-body">
    <div class="col-12 p-0">
      <div class="form-group">
        <label
          for="basicTextarea"
          class="w-100 align-items-center d-flex justify-content-between"
        >
          <div class="d-flex align-items-center">
            <p class="mb-0">Lượng dùng hoạt động ngày</p>
            <span class="text-primary ml-25">{{ dateSelected }}</span>
          </div>
          <div class="">
            <button
              class="btn btn-sm ml-auto p-0"
              (click)="modal.dismiss('Cross click')"
            >
              <img src="assets/images/icons/x.svg" alt="x" />
            </button></div
        ></label>
      </div>
    </div>
    <div class="input-group input-group-merge mb-2 col-5 p-0">
      <div class="input-group-prepend">
        <span class="input-group-text" id="basic-addon-search2"
          ><i data-feather="search"></i
        ></span>
      </div>
      <input
        type="text"
        class="form-control"
        placeholder="Tìm kiếm theo tên, email, tổ chức..."
        aria-label="Search..."
        aria-describedby="basic-addon-search2"
        (input)="updateFilter($event)"
      />
    </div>
    <ngx-datatable
      #tableRowDetails
      [rows]="filteredData"
      [rowHeight]="58"
      class="bootstrap core-bootstrap cursor"
      [columnMode]="ColumnMode.force"
      [headerHeight]="40"
      [footerHeight]="50"
      [scrollbarH]="true"
      [limit]="10"
      [count]="filteredData.length"
    >
      <ngx-datatable-column name="Họ tên" prop="fullname" [width]="250">
      </ngx-datatable-column>

      <ngx-datatable-column name="Email" prop="email" [width]="150">
      </ngx-datatable-column>
      <ngx-datatable-column name="Tổ chức" prop="organName" [width]="150">
      </ngx-datatable-column>
    </ngx-datatable>
  </div>
</ng-template>
<ng-template #modalNewUser let-modal>
  <div class="modal-body">
    <div class="col-12 p-0">
      <div class="form-group">
        <label
          for="basicTextarea"
          class="w-100 align-items-center d-flex justify-content-between"
        >
          <div class="d-flex align-items-center">
            <p class="mb-0">Lượng người dùng mới ngày</p>
            <span class="text-primary ml-25">{{ dateSelected }}</span>
          </div>
          <div class="">
            <button
              class="btn btn-sm ml-auto p-0"
              (click)="modal.dismiss('Cross click')"
            >
              <img src="assets/images/icons/x.svg" alt="x" />
            </button></div
        ></label>
      </div>
    </div>
    <div class="input-group input-group-merge mb-2 col-5 p-0">
      <div class="input-group-prepend">
        <span class="input-group-text" id="basic-addon-search2"
          ><i data-feather="search"></i
        ></span>
      </div>
      <input
        type="text"
        class="form-control"
        placeholder="Tìm kiếm theo tên, email, tổ chức..."
        aria-label="Search..."
        aria-describedby="basic-addon-search2"
        (input)="updateFilterNewUser($event)"
      />
    </div>
    <ngx-datatable
      #tableRowDetails
      [rows]="filteredDataNewUserByDay"
      [rowHeight]="58"
      class="bootstrap core-bootstrap cursor"
      [columnMode]="ColumnMode.force"
      [headerHeight]="40"
      [footerHeight]="50"
      [scrollbarH]="true"
      [limit]="10"
      [count]="filteredDataNewUserByDay.length"
    >
      <ngx-datatable-column name="Họ tên" prop="fullname" [width]="250">
      </ngx-datatable-column>

      <ngx-datatable-column name="Email" prop="email" [width]="150">
      </ngx-datatable-column>
      <ngx-datatable-column name="Tổ chức" prop="organName" [width]="150">
      </ngx-datatable-column>
    </ngx-datatable>
  </div>
</ng-template>
