import { Component, OnInit, ViewChild, ViewEncapsulation } from "@angular/core";
import { NavigationEnd, Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ColumnMode } from "@swimlane/ngx-datatable";
import {
  ApexChart,
  ApexDataLabels,
  ApexFill,
  ApexLegend,
  ApexMarkers,
  ApexNonAxisChartSeries,
  ApexPlotOptions,
  ApexResponsive,
  ApexStates,
  ApexStroke,
  ApexTooltip,
  ApexXAxis,
  ApexYAxis,
} from "ng-apexcharts";
import { FlatpickrOptions } from "ng2-flatpickr";
import { filter } from "rxjs/operators";
import { FormatTime } from "../../../../../util/formatTime";
import { DashboardService } from "./dashboard.service";

@Component({
  selector: "app-dashboard",
  templateUrl: "./dashboard.component.html",
  styleUrls: ["./dashboard.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class DashboardComponent implements OnInit {
  @ViewChild("modalDetailUserActive") modalDetailUserActive!: any;
  @ViewChild("modalNewUser") modalNewUser!: any;

  public isUrlAdmin: boolean = false;
  public dateSelected: any;
  public dataDashboard: any;
  private tooltipShadow = "rgba(0, 0, 0, 0.25)";
  private successColorShade = "#198754";
  private warningLightColor = "#FDAC34";
  private grid_line_color = "rgba(200, 200, 200, 0.2)"; // RGBA color helps in dark layout
  private labelColor = "#6e6b7b";
  private lineChartNewUserColor = "#6766DE";
  private lineChartConversationColor = "#50AEA8";
  public listOrganization = [];
  public listDate = [
    { name: "Tuần", id: "week" },
    { name: "Tháng", id: "month" },
    { name: "Năm", id: "year" },
  ];
  public configDate: string = "day";
  public selectedOrganization = "";
  public startDate;
  public endDate;
  public isChooseOrgan: boolean = false;
  public basicDateOptions: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
    mode: "range",
    onReady: (selectedDates, dateStr, instance) => {
      // Tạo footer
      const footer = document.createElement("div");
      footer.classList.add(
        "flatpickr-footer",
        "d-flex",
        "justify-content-between",
        "p-50"
      );
      footer.style.borderTop = "1px solid rgb(129, 129, 129)";

      footer.innerHTML = `
        <button class="btn btn-outline-primary btn-sm text-primary" data-range="7">7 ngày</button>
        <button class="btn btn-outline-primary btn-sm text-primary" data-range="30">30 ngày</button>
        <button class="btn btn-outline-primary btn-sm text-primary" data-range="90">90 ngày</button>
      `;

      instance.calendarContainer.appendChild(footer);

      // Gắn sự kiện click
      footer.querySelectorAll("button").forEach((btn) => {
        btn.addEventListener("click", () => {
          const days = parseInt(btn.getAttribute("data-range") || "0", 10);
          const today = new Date();
          const start = new Date();
          start.setDate(today.getDate() - (days - 1)); // Trừ (days - 1) để tính cả hôm nay

          // Set range ngày
          instance.setDate([start, today], true);
          instance.close();
        });
      });
    },
  };
  public plugins = [
    {
      beforeInit(chart) {
        chart.legend.afterFit = function () {
          this.height += 20;
        };
      },
    },
  ];
  private chartColors = {
    column: {
      series1: "#826af9",
      series2: "#d2b0ff",
      bg: "#f8d3ff",
    },
    success: {
      shade_100: "#7eefc7",
      shade_200: "#06774f",
    },
    donut: {
      series1: "#D18CD7",
      series2: "#00d4bd",
      series3: "#826bf8",
      series4: "#2b9bf4",
      series5: "#FFA1A1",
    },
    area: {
      series3: "#a4f8cd",
      series2: "#60f2ca",
      series1: "#2bdac7",
    },
  };
  
  public chartUserActivity = {
    chartType: "bar",
    datasets: [
      {
        data: [],
        backgroundColor: this.successColorShade,
        borderColor: "transparent",
        hoverBackgroundColor: this.successColorShade,
        hoverBorderColor: this.successColorShade,
        barThickness: 15,
      },
    ],
    labels: [],
    options: {
      elements: {
        rectangle: {
          borderWidth: 2,
          borderSkipped: "bottom",
        },  
      },
      animation: {
        duration: 500,
        onComplete: function () {
          const chart = this.chart;
          const ctx = chart.ctx;

          ctx.font = "bold 12px sans-serif";
          ctx.fillStyle = "#000";
          ctx.textAlign = "center";
          ctx.textBaseline = "bottom";

          chart.data.datasets.forEach(function (dataset, i) {
            const meta = chart.getDatasetMeta(i);
            meta.data.forEach(function (bar, index) {
              const data = dataset.data[index];
              if (data !== null && data !== undefined) {
                ctx.fillText(data, bar._model.x, bar._model.y - 5);
              }
            });
          });
        },
      },
      responsive: true,
      maintainAspectRatio: false,
      responsiveAnimationDuration: 500,
      legend: {
        display: false,
      },
      tooltips: {
        // Updated default tooltip UI
        shadowOffsetX: 1,
        shadowOffsetY: 1,
        shadowBlur: 8,
        shadowColor: this.tooltipShadow,
        backgroundColor: "#FFF",
        titleFontColor: "#000",
        bodyFontColor: "#000",
        callbacks: {
          // Nếu muốn tuỳ chỉnh định dạng tooltip
          label: function (tooltipItem, data) {
            return `${tooltipItem.yLabel} người`;
          },
        },
      },
      plugins: {
        datalabels: {
          color: "#000", // Màu chữ
          anchor: "end", // Gắn vào đầu cột
          align: "top", // Canh trên
          formatter: (value) => `${value}`, // Hiển thị số
          font: {
            weight: "bold",
            size: 12,
          },
        },
      },
      scales: {
        xAxes: [
          {
            
            display: true,
            gridLines: {
              // display: true,
              // color: this.grid_line_color,
              // zeroLineColor: this.grid_line_color,
              display: false, // ❌ Tắt ô vuông nền trục Y
              drawBorder: false,
            },
            scaleLabel: {
              display: true,
            },
            ticks: {
              fontColor: this.labelColor,
            },
          },
        ],
        yAxes: [
          {
            display: true,
            gridLines: {
              // color: this.grid_line_color,
              // zeroLineColor: this.grid_line_color,
              display: false, // ❌ Tắt ô vuông nền trục Y
              drawBorder: false,
            },
            ticks: {
              beginAtZero: true,
              stepSize: 2,
              min: 0,
              max: 10,
              
              fontColor: this.labelColor,
            },
            
          },
        ],
        
      },
    },
    legend: false,
  };




  public lineChartNewUser = {
    chartType: "line",
    options: {
      responsive: true,
      maintainAspectRatio: false,
      backgroundColor: false,
      hover: {
        mode: "label",
        intersect: true,
      },
      tooltips: {
        enabled: true,
        mode: "nearest",
        intersect: true,
        backgroundColor: "#FFF",
        titleFontColor: "#000",
        bodyFontColor: "#000",
        shadowOffsetX: 1,
        shadowOffsetY: 1,
        shadowBlur: 8,
        shadowColor: this.tooltipShadow,
        displayColors: false,
        callbacks: {
          // Nếu muốn tuỳ chỉnh định dạng tooltip
          label: function (tooltipItem, data) {
            return `Giá trị: ${tooltipItem.yLabel}`;
          },
        },
      },
      scales: {
        xAxes: [
          {
            display: true,
            scaleLabel: {
              display: true,
            },
            gridLines: {
              display: true,
              color: this.grid_line_color,
              zeroLineColor: this.grid_line_color,
            },
            ticks: {
              fontColor: this.labelColor,
            },
          },
        ],
        yAxes: [
          {
            display: true,
            scaleLabel: {
              display: true,
            },
            ticks: {
              stepSize: 100,
              beginAtZero: true,
              min: 0,
              max: 400,
              fontColor: this.labelColor,
            },
            gridLines: {
              display: true,
              color: this.grid_line_color,
              zeroLineColor: this.grid_line_color,
            },
          },
        ],
      },
      layout: {
        padding: {
          top: -15,
          bottom: -25,
          left: -15,
        },
      },
      legend: {
        display: false,
      },
    },

    labels: [],
    datasets: [
      {
        data: [],
        borderColor: this.lineChartNewUserColor,
        lineTension: 0.5,
        pointStyle: "circle",
        backgroundColor: this.lineChartNewUserColor,
        fill: false,
        pointRadius: 1,
        pointHoverRadius: 5,
        pointHoverBorderWidth: 5,
        pointBorderColor: "transparent",
        pointHoverBorderColor: "#fff",
        pointHoverBackgroundColor: this.lineChartNewUserColor,
        pointShadowOffsetX: 1,
        pointShadowOffsetY: 1,
        pointShadowBlur: 5,
        pointShadowColor: this.tooltipShadow,
      },
    ],
  };
  public lineChartSumConversation = {
    chartType: "line",
    options: {
      responsive: true,
      maintainAspectRatio: false,
      backgroundColor: false,
      hover: {
        mode: "index", // Hover theo trục x
        intersect: false, // Không cần chạm đúng vào điểm dữ liệu
      },
      tooltips: {
        enabled: true,
        mode: "index", // Hiển thị tooltip theo trục x
        intersect: false, // Không cần chạm chính xác vào điểm
        backgroundColor: "#FFF",
        titleFontColor: "#000",
        bodyFontColor: "#000",
        shadowOffsetX: 1,
        shadowOffsetY: 1,
        shadowBlur: 8,
        shadowColor: this.tooltipShadow,
        displayColors: false,
        callbacks: {
          label: function (tooltipItem, data) {
            return `Giá trị: ${tooltipItem.yLabel}`;
          },
        },
      },
      scales: {
        xAxes: [
          {
            display: true,
            scaleLabel: {
              display: true,
            },
            gridLines: {
              display: true,
              color: this.grid_line_color,
              zeroLineColor: this.grid_line_color,
            },
            ticks: {
              fontColor: this.labelColor,
            },
          },
        ],
        yAxes: [
          {
            display: true,
            scaleLabel: {
              display: true,
            },
            ticks: {
              stepSize: 100,
              min: 0,
              max: 400,
              fontColor: this.labelColor,
            },
            gridLines: {
              display: true,
              color: this.grid_line_color,
              zeroLineColor: this.grid_line_color,
            },
          },
        ],
      },
      layout: {
        padding: {
          top: -15,
          bottom: -25,
          left: -15,
        },
      },
      legend: {
        display: false,
      },
    },

    labels: [],
    datasets: [
      {
        data: [],
        borderColor: this.lineChartNewUserColor,
        lineTension: 0.5,
        pointStyle: "circle",
        backgroundColor: this.lineChartNewUserColor,
        fill: false,
        pointRadius: 1,
        pointHoverRadius: 5,
        pointHoverBorderWidth: 5,
        pointBorderColor: "transparent",
        pointHoverBorderColor: "#fff",
        pointHoverBackgroundColor: this.lineChartNewUserColor,
        pointShadowOffsetX: 1,
        pointShadowOffsetY: 1,
        pointShadowBlur: 5,
        pointShadowColor: this.tooltipShadow,
      },
    ],
  };
  public lineChartDetailConversation: any;
  public lineChartDetailConversation2: any;
  public apexDonutChart: Partial<ChartOptions2>;
  public isMenuToggled = false;
  public role = "";
  public listUserActiveByDay = [];
  public listNewUserByDay = [];
  public filteredData = [];
  public filteredDataNewUserByDay = [];
  public ColumnMode = ColumnMode;

  constructor(
    private dashboardService: DashboardService,
    private router: Router,
    private modalService: NgbModal
  ) {
    this.apexDonutChart = {
      series: [],
      chart: {
        height: "100%",
        type: "donut",
      },
      colors: [
        this.chartColors.donut.series1,
        this.chartColors.donut.series2,
        this.chartColors.donut.series3,
        this.chartColors.donut.series5,
      ],
      plotOptions: {
        pie: {
          donut: {
            labels: {
              show: true,
              name: {
                fontSize: "2rem",
                fontFamily: "Montserrat",
              },
              value: {
                fontSize: "1rem",
                fontFamily: "Montserrat",
                formatter: function (val) {
                  return parseInt(val) + " người";
                },
              },
            },
          },
        },
      },
      legend: {
        show: true,
        position: "bottom",
        floating: false,
        formatter: function (val) {
          return val.length > 15 ? val.substring(0, 12) + "..." : val;
        },
      },
      labels: [],
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {},
            legend: {
              position: "left",
            },
          },
        },
      ],
    };
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        if (event.urlAfterRedirects.includes("super-admin")) {
          this.isUrlAdmin = true;
        } else {
          this.isUrlAdmin = false;
        }
      });
  }

  ngOnInit(): void {
    this.role = JSON.parse(localStorage.getItem("current_User")).role;
    this.startDate = new FormatTime().formatDateTime(
      new Date(new Date().setDate(new Date().getDate() - 30)),
      "yyyy-MM-dd"
    );
    this.endDate = new FormatTime().formatDateTime(new Date(), "yyyy-MM-dd");
    (this.basicDateOptions as any).defaultDate = [this.startDate, this.endDate];
    if (this.role == "SUPER_ADMIN") {
      this.getAllOrgan();
    }
    this.getDataDashboard();
  }
  getAllOrgan() {
    this.dashboardService.getAllOrgan().subscribe((res) => {
      if (res.results)
        this.listOrganization = this.buildFlatTreeForNgSelect(res.results);
      else this.listOrganization = this.buildFlatTreeForNgSelect(res);
    });
  }
  getDataDashboard() {
    // Số liệu tổng quan
    this.dashboardService
      .getOverview(this.selectedOrganization, this.startDate, this.endDate, this.configDate)
      .subscribe((res) => {
        this.dataDashboard = res;
      });

    // Donut tổ chức (super admin)
    if (this.role === "SUPER_ADMIN") {
      this.dashboardService
        .getOrganizationsData(this.selectedOrganization, this.startDate, this.endDate, this.configDate)
        .subscribe((res) => {
          this.apexDonutChart.series = res?.map((item) => item.user_count);
          this.apexDonutChart.labels = res?.map((item) => item.organization_name);
          const randomColors = this.apexDonutChart.labels?.map(() => this.getRandomColor());
          this.apexDonutChart.colors = randomColors;
        });
    }

    // Chart active users
    this.dashboardService
      .getChartActiveUsers(this.selectedOrganization, this.startDate, this.endDate, this.configDate)
      .subscribe((res) => {
        const dataChartActivityUser = res?.map((item) => item.count);
        const labelChartActivityUser = res?.map((item) => item.formatted_date);
        const { stepSizeUserActivity, maxUserActivity } =
          this.getYAxisOptionsFromDataUserActivity(dataChartActivityUser);
        this.chartUserActivity = {
          ...this.chartUserActivity,
          labels: labelChartActivityUser,
          datasets: [
            {
              ...this.chartUserActivity.datasets[0],
              data: dataChartActivityUser,
            },
          ],
          options: {
            ...this.chartUserActivity.options,
            scales: {
              ...this.chartUserActivity.options.scales,
              yAxes: [
                {
                  ...this.chartUserActivity.options.scales.yAxes[0],
                  ticks: {
                    ...this.chartUserActivity.options.scales.yAxes[0].ticks,
                    stepSize: stepSizeUserActivity,
                    max: maxUserActivity,
                  },
                },
              ],
            },
          },
        };
      });

    // Chart user mới
    this.dashboardService
      .getChartNewUsers(this.selectedOrganization, this.startDate, this.endDate, this.configDate)
      .subscribe((res) => {
        const dataChartNewUser = res?.map((item) => item.count);
        const labelChartNewUser = res?.map((item) => item.formatted_date);
        const { stepNewUser, maxnewuser } = this.getYAxisOptionsFromDataNewUser(dataChartNewUser);
        this.lineChartNewUser = {
          ...this.lineChartNewUser,
          labels: labelChartNewUser,
          datasets: [
            {
              ...this.lineChartNewUser.datasets[0],
              data: dataChartNewUser,
            },
          ],
          options: {
            ...this.lineChartNewUser.options,
            scales: {
              ...this.lineChartNewUser.options.scales,
              yAxes: [
                {
                  ...this.lineChartNewUser.options.scales.yAxes[0],
                  ticks: {
                    ...this.lineChartNewUser.options.scales.yAxes[0].ticks,
                    stepSize: stepNewUser,
                    max: maxnewuser,
                  },
                },
              ],
            },
          },
        };
      });

    // Chart conversation
    this.dashboardService
      .getChartConversations(this.selectedOrganization, this.startDate, this.endDate, this.configDate)
      .subscribe((res) => {
        const dataChartNewConversation = res?.map((item) => item.count);
        const labelChartSumConversation = res?.map((item) => item.formatted_date);
        const { stepSizeSumConversation, maxsizesumconversation } =
          this.getYAxisOptionsFromDataNewConversation(dataChartNewConversation);
        this.lineChartSumConversation = {
          ...this.lineChartSumConversation,
          labels: labelChartSumConversation,
          datasets: [
            {
              ...this.lineChartSumConversation.datasets[0],
              data: dataChartNewConversation,
            },
          ],
          options: {
            ...this.lineChartSumConversation.options,
            scales: {
              ...this.lineChartSumConversation.options.scales,
              yAxes: [
                {
                  ...this.lineChartSumConversation.options.scales.yAxes[0],
                  ticks: {
                    ...this.lineChartSumConversation.options.scales.yAxes[0].ticks,
                    stepSize: stepSizeSumConversation,
                    max: maxsizesumconversation,
                  },
                },
              ],
            },
          },
        };
        this.lineChartDetailConversation = this.generateLineChartDetailConversation(res);
        this.lineChartDetailConversation2 = this.generateLineChartDetailConversation2(res);
      });
  }
  generateLineChartDetailConversation(dataDetaiUseConversation) {
    const labels = dataDetaiUseConversation.map((item) => item.formatted_date);

    const featureNames = [
      "chat_messages",
      "legal_search",
      "search_law_clauses",
    ];

    // Khởi tạo dữ liệu trống cho từng feature
    const featureDataMap = {
      chat_messages: [],
      legal_search: [],
      search_law_clauses: [],
    };

    // Gán giá trị theo ngày
    dataDetaiUseConversation.forEach((item) => {
      featureDataMap.chat_messages.push(item.chat_messages);

      const features = item.feature_counts || {};
      featureDataMap.legal_search.push(features.legal_search || 0);
      featureDataMap.search_law_clauses.push(features.search_law_clauses || 0);
    });

    const featureColors = {
      chat_messages: "#FF6384",
      legal_search: "#36A2EB",
      search_law_clauses: "#9966FF",
    };
    const featureLabelsVi = {
      chat_messages: "Tin nhắn chatbot",
      legal_search: "Tìm kiếm",
      search_law_clauses: "Rà soát",
    };

    const datasets = featureNames.map((name) => {
      const color = featureColors[name] || "#000000";
      return {
        label: featureLabelsVi[name] || name,
        data: featureDataMap[name],
        borderColor: color, // màu đường
        backgroundColor: color, // màu fill (tooltip)
        pointBackgroundColor: color, // màu điểm và tooltip color box
        pointBorderColor: color, // màu viền điểm
        fill: false,
      };
    });

    return {
      chartType: "line",
      options: {
        responsive: true,
        maintainAspectRatio: false,
        hover: {
          mode: "index",
          intersect: false,
        },
        tooltips: {
          enabled: true,
          mode: "index",
          intersect: false,
          backgroundColor: "#FFF",
          titleFontColor: "#000",
          bodyFontColor: "#000",
          shadowOffsetX: 1,
          shadowOffsetY: 1,
          shadowBlur: 8,
          shadowColor: "#999",
          displayColors: true,
          callbacks: {
            label: function (tooltipItem, data) {
              return `${data.datasets[tooltipItem.datasetIndex].label}: ${
                tooltipItem.yLabel
              }`;
            },
          },
        },
        scales: {
          xAxes: [
            {
              display: true,
              ticks: { fontColor: "#888" },
              gridLines: {
                color: "#eee",
                zeroLineColor: "#eee",
              },
            },
          ],
          yAxes: [
            {
              display: true,
              ticks: {
                beginAtZero: true,
                fontColor: "#888",
              },
              gridLines: {
                color: "#eee",
                zeroLineColor: "#eee",
              },
            },
          ],
        },
        legend: {
          display: true,
        },
      },
      labels,
      datasets,
    };
  }
  generateLineChartDetailConversation2(dataDetaiUseConversation) {
    const labels = dataDetaiUseConversation.map((item) => item.formatted_date);

    const featureNames = [
      "note_create",
      "document_create",
      "compare",
      "create_workspace",
    ];

    // Khởi tạo dữ liệu trống cho từng feature
    const featureDataMap = {
      note_create: [],
      document_create: [],
      compare: [],
      create_workspace: [],
    };

    // Gán giá trị theo ngày
    dataDetaiUseConversation.forEach((item) => {
      const features = item.feature_counts || {};
      featureDataMap.note_create.push(features.note_create || 0);
      featureDataMap.document_create.push(features.document_create || 0);
      featureDataMap.compare.push(features.compare || 0);
      featureDataMap.create_workspace.push(features.create_workspace || 0);
    });

    const featureColors = {
      note_create: "#FFCE56",
      document_create: "#4BC0C0",
      compare: "#FF9F40",
      create_workspace: "#8BC34A",
    };

    const featureLabelsVi = {
      note_create: "Tạo ghi chú mới",
      document_create: "Tải lên tài liệu",
      compare: "So sánh điều",
      create_workspace: "Tạo không gian dự án",
    };

    const datasets = featureNames.map((name) => {
      const color = featureColors[name] || "#000000";
      return {
        label: featureLabelsVi[name] || name,
        data: featureDataMap[name],
        borderColor: color, // màu đường
        backgroundColor: color, // màu fill (tooltip)
        pointBackgroundColor: color, // màu điểm và tooltip color box
        pointBorderColor: color, // màu viền điểm
        fill: false,
      };
    });

    return {
      chartType: "line",
      options: {
        responsive: true,
        maintainAspectRatio: false,
        hover: {
          mode: "index",
          intersect: false,
        },
        tooltips: {
          enabled: true,
          mode: "index",
          intersect: false,
          backgroundColor: "#FFF",
          titleFontColor: "#000",
          bodyFontColor: "#000",
          shadowOffsetX: 1,
          shadowOffsetY: 1,
          shadowBlur: 8,
          shadowColor: "#999",
          displayColors: true,
          callbacks: {
            label: function (tooltipItem, data) {
              return `${data.datasets[tooltipItem.datasetIndex].label}: ${
                tooltipItem.yLabel
              }`;
            },
          },
        },
        scales: {
          xAxes: [
            {
              display: true,
              ticks: { fontColor: "#888" },
              gridLines: {
                color: "#eee",
                zeroLineColor: "#eee",
              },
            },
          ],
          yAxes: [
            {
              display: true,
              ticks: {
                beginAtZero: true,
                fontColor: "#888",
              },
              gridLines: {
                color: "#eee",
                zeroLineColor: "#eee",
              },
            },
          ],
        },
        legend: {
          display: true,
        },
      },
      labels,
      datasets,
    };
  }
  changeOrganization(event) {
    if (event) {
      this.selectedOrganization = event.id;
      this.isChooseOrgan = true;
    } else {
      this.selectedOrganization = "";
      this.isChooseOrgan = false;
    }
    this.getDataDashboard();
  }
  buildFlatTreeForNgSelect(data: any[]): [] {
    // Bước 1: Tạo map id → node
    const nodeMap: { [id: string]: any } = {};
    const roots: any[] = [];

    data.forEach((org) => {
      nodeMap[org.id] = { id: org.id, name: org.name, children: [] };
    });

    // Bước 2: Gắn cha – con
    data.forEach((org) => {
      const node = nodeMap[org.id];
      if (org.parent_organization && nodeMap[org.parent_organization]) {
        nodeMap[org.parent_organization].children!.push(node);
      } else {
        roots.push(node);
      }
    });

    // Bước 3: Duyệt cây và tạo danh sách phẳng với indent
    const result: any = [];

    function traverse(node: any, level: number) {
      result.push({
        id: node.id,
        name: `${"— ".repeat(level)}${node.name}`,
      });
      node.children?.forEach((child) => traverse(child, level + 1));
    }

    roots.forEach((root) => traverse(root, 0));
    return result;
  }
  changeDate(event) {
    if (event) {
      this.configDate = event.id;
      this.getDataDashboard();
    } else {
      this.configDate = "day";
      this.getDataDashboard();
    }
  }
  onChangDate(event) {
    const date = event.target.value;
    if (date.includes("đến")) {
      const [start, end] = date.split("đến");
      this.startDate = start.trim();
      this.endDate = end.trim();
      this.getDataDashboard();
    }
  }
  getRandomColor() {
    // Hue: 0–360, Saturation: 70–100%, Lightness: 30–50%
    const h = Math.floor(Math.random() * 360);
    const s = Math.floor(Math.random() * 31) + 70; // 70–100%
    const l = Math.floor(Math.random() * 21) + 30; // 30–50%
    return `hsl(${h}, ${s}%, ${l}%)`;
  }
  getYAxisOptionsFromDataUserActivity(data, desiredSteps = 5) {
    const maxDataValue = Math.max(...data);

    // Nếu toàn bộ dữ liệu là 0, set max = 1 để luôn hiện trục Y
    if (maxDataValue === 0) {
      return {
        stepSizeUserActivity: 1,
        maxUserActivity: 1,
      };
    }

    // Tính stepSize đẹp
    const rawStep = maxDataValue / desiredSteps;
    const magnitude = Math.pow(10, Math.floor(Math.log10(rawStep)));
    const stepSizeUserActivity = Math.ceil(rawStep / magnitude) * magnitude;

    // Làm tròn max lên bội số gần nhất của stepSizeUserActivity
    const maxUserActivity =
      Math.ceil(maxDataValue / stepSizeUserActivity) * stepSizeUserActivity;

    return {
      stepSizeUserActivity,
      maxUserActivity,
    };
  }
  getYAxisOptionsFromDataNewUser(data, desiredSteps = 5) {
    const maxDataValue = Math.max(...data);

    // Tính stepSize đẹp
    const rawStep = maxDataValue / desiredSteps;
    const magnitude = Math.pow(10, Math.floor(Math.log10(rawStep)));
    const stepNewUser = Math.ceil(rawStep / magnitude) * magnitude;

    // Làm tròn max lên bội số gần nhất của stepSize
    const maxnewuser = Math.ceil(maxDataValue / stepNewUser) * stepNewUser;

    return {
      stepNewUser,
      maxnewuser,
    };
  }
  getYAxisOptionsFromDataNewConversation(data, desiredSteps = 5) {
    const maxDataValue = Math.max(...data);

    // Tính stepSize đẹp
    const rawStep = maxDataValue / desiredSteps;
    const magnitude = Math.pow(10, Math.floor(Math.log10(rawStep)));
    const stepSizeSumConversation = Math.ceil(rawStep / magnitude) * magnitude;

    // Làm tròn max lên bội số gần nhất của stepSize
    const maxsizesumconversation =
      Math.ceil(maxDataValue / stepSizeSumConversation) *
      stepSizeSumConversation;

    return {
      stepSizeSumConversation,
      maxsizesumconversation,
    };
  }
  exportUserActivity() {
    this.dashboardService.downloadUserActivity(
      this.startDate,
      this.endDate,
      this.selectedOrganization
    );
  }
  exportNewUserActivity() {
    this.dashboardService.downloadNewUserActivity(
      this.startDate,
      this.endDate,
      this.selectedOrganization
    );
  }
  onChartClick(event: any) {
    if (event.active && event.active.length > 0) {
      const chartElement = event.active[0];
      const datasetIndex = chartElement._datasetIndex;
      const dataIndex = chartElement._index;

      // Lấy label
      const label = new FormatTime().formatDateTime(
        this.chartUserActivity.labels[dataIndex],
        "yyyy-MM-dd"
      );
      this.dateSelected = this.chartUserActivity.labels[dataIndex];
      // Nếu muốn lấy luôn value:
      // const value =
      //   this.chartUserActivity.datasets[datasetIndex].data[dataIndex];

      this.getUserActiveByDay(label, this.selectedOrganization);
    }
  }
  onChartClickNewUser(event) {
    if (event.active && event.active.length > 0) {
      const chartElement = event.active[0];
      const datasetIndex = chartElement._datasetIndex;
      const dataIndex = chartElement._index;

      // Lấy label
      const label = new FormatTime().formatDateTime(
        this.chartUserActivity.labels[dataIndex],
        "yyyy-MM-dd"
      );
      this.dateSelected = this.chartUserActivity.labels[dataIndex];
      console.log(label);

      // Nếu muốn lấy luôn value:
      // const value =
      //   this.chartUserActivity.datasets[datasetIndex].data[dataIndex];

      this.getNewUserByDay(label, this.selectedOrganization);
    }
  }
  getNewUserByDay(date, organId) {
    this.dashboardService
      .getNewUserByDay(date, organId)
      .subscribe((res: any) => {
        this.listNewUserByDay = res.users;
        this.filteredDataNewUserByDay = [...this.listNewUserByDay];
        this.modalOpen(this.modalNewUser, "lg");
      });
  }
  getUserActiveByDay(date, organId) {
    this.dashboardService
      .getUserActiveByDay(date, organId)
      .subscribe((res: any) => {
        this.listUserActiveByDay = res.users;
        this.filteredData = [...this.listUserActiveByDay];
        this.modalOpen(this.modalDetailUserActive, "lg");
      });
  }
  modalOpen(modalSM, size) {
    this.modalService.open(modalSM, {
      centered: true,
      size: size,
    });
  }
  updateFilter(event: any) {
    const val = event.target.value.toLowerCase();
    // filter theo tên, email hoặc tổ chức
    this.filteredData = this.listUserActiveByDay.filter((user) => {
      return (
        user.fullname.toLowerCase().includes(val) ||
        user.email.toLowerCase().includes(val) ||
        user.organName.toLowerCase().includes(val)
      );
    });
  }
  updateFilterNewUser(event: any) {
    const val = event.target.value.toLowerCase();
    // filter theo tên, email hoặc tổ chức
    this.filteredDataNewUserByDay = this.listNewUserByDay.filter((user) => {
      return (
        user.fullname.toLowerCase().includes(val) ||
        user.email.toLowerCase().includes(val) ||
        user.organName.toLowerCase().includes(val)
      );
    });
  }
}
export interface ChartOptions2 {
  // Apex-non-axis-chart-series
  series?: ApexNonAxisChartSeries;
  chart?: ApexChart;
  stroke?: ApexStroke;
  tooltip?: ApexTooltip;
  dataLabels?: ApexDataLabels;
  fill?: ApexFill;
  colors?: string[];
  legend?: ApexLegend;
  labels?: any;
  plotOptions?: ApexPlotOptions;
  responsive?: ApexResponsive[];
  markers?: ApexMarkers[];
  xaxis?: ApexXAxis;
  yaxis?: ApexYAxis;
  states?: ApexStates;
}
