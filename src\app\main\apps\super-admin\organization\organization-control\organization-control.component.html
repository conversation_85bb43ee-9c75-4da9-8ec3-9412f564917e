<div class="modal-body" tabindex="0" ngbAutofocus>
  <div class="form-group">
    <label
      for="basicTextarea"
      class="w-100 align-items-center d-flex justify-content-between"
      >{{ title }}
      <div class="">
        <button
          class="btn btn-sm ml-auto p-0"
          (click)="modal.dismiss('Cross click')"
        >
          <img src="assets/images/icons/x.svg" alt="x" />
        </button></div
    ></label>
  </div>
  <div class="container-xxl p-0">
    <form [formGroup]="organizationForm" (ngSubmit)="submitSaveOrganization()">
      <div class="row">
        <div class="col-md-12 mb-1">
          <label for="orgName" class="form-label"
            >{{ role == "SUPER_ADMIN" ? "Tên tổ chức" : "Tên phòng ban" }}
            <span class="text-danger">*</span>
          </label>
          <input
            id="orgName"
            type="text"
            class="form-control"
            formControlName="name"
            placeholder="Nhập"
            appFormControlValidation
          />
        </div>
        <div class="col-12 mb-2" *ngIf="role == 'SUPER_ADMIN'">
          <label>Trực thuộc tổ chức</label>
          <ng-select
            [items]="listOrganization"
            placeholder="Chọn"
            bindLabel="name"
            bindValue="id"
            formControlName="parent_organization"
          >
          </ng-select>
        </div>
        <!-- <div class="col-md-12 mb-1">
          <label for="description" class="form-label">Mô tả</label>
          <input
            id="description"
            type="text"
            class="form-control"
            formControlName="description"
            placeholder="Nhập"
          />
        </div> -->
        <!-- <div class="col-md-12 mb-1" *ngIf="type == FormType.Create">
          <label>Trạng thái</label>
          <ng-select
            [clearable]="false"
            [items]="statusOrgan"
            bindLabel="label"
            bindValue="value"
            placeholder="Chọn"
            formControlName="status"
          >
          </ng-select>
        </div> -->
        <div class="col-12 mb-1">
          <label class="form-label">Họ và tên người đại diện</label>
          <input
            id="description"
            type="text"
            class="form-control"
            formControlName="represent_people"
            placeholder="Nhập"
            appFormControlValidation
          />
        </div>
        <div class="col-md-12 mb-1">
          <label for="email" class="form-label">Email</label>
          <input
            id="email"
            type="email"
            class="form-control"
            formControlName="email"
            maxlength="255"
            appFormControlValidation="Vui lòng nhập đúng định dạng email và không quá 255 ký tự"
            placeholder="Nhập"
          />
        </div>
        <div class="col-md-12 mb-1">
          <label for="phoneNumber" class="form-label">Số điện thoại</label>
          <input
            id="phoneNumber"
            type="text"
            class="form-control"
            formControlName="phone"
            appFormControlValidation="Số điện thoại phải bắt đầu bằng số 0 và có 10 chữ số"
            placeholder="Nhập"
          />
        </div>
        <div class="col-md-12 mb-1">
          <label for="address" class="form-label">Địa chỉ</label>
          <input
            id="address"
            type="text"
            class="form-control"
            formControlName="address"
            placeholder="Nhập"
            appFormControlValidation
          />
        </div>
      </div>
      <div class="w-100 justify-content-end d-flex">
        <button
          type="button"
          rippleEffect
          class="btn btn-secondary mr-1"
          (click)="modal.close('Cross click'); onCancel()"
        >
          Huỷ
        </button>
        <button rippleEffect type="submit" class="btn btn-primary" [disabled]="organizationForm.invalid">
          Xác nhận
        </button>
      </div>
    </form>
  </div>
</div>
