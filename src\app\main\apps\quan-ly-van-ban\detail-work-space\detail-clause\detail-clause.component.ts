import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ColumnMode, DatatableComponent } from "@swimlane/ngx-datatable";
import { WebSocketService } from "app/auth/service/webSocket.service";
import { ViewDetailFileService } from "app/layout/components/view-detail-file/view-detail-file.service";
import { ShowContent } from "app/models/ShowContent";
import { ShowSideBar } from "app/models/ShowSideBa";
import { ProgressToastStore } from "app/shared/progress-toast.shared";
import { environment } from "environments/environment";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { debounceTime, distinctUntilChanged, takeUntil } from "rxjs/operators";
import Swal from "sweetalert2";
import { BoSungVanBanDieuKhoanService } from "../bo-sung-van-ban-dieu-khoan/bo-sung-van-ban-dieu-khoan.service";
import { ComnpareClauseChatbotService } from "../compare-clause-chatbot/comnpare-clause-chatbot.service";
import { DetailWorkSpaceService } from "../detail-work-space.service";
import { ListDocumentService } from "../list-document/list-document.service";
import { DetailClauseService } from "./detail-clause.service";

@Component({
  selector: "app-detail-clause",
  templateUrl: "./detail-clause.component.html",
  styleUrls: ["./detail-clause.component.scss"],
})
export class DetailClauseComponent implements OnInit {
  @ViewChild("tableRowDetails") tableRowDetails: DatatableComponent;
  @ViewChild("detailCompareModal") detailCompareModal!: any;
  @ViewChild("contentClauseModal") contentClauseModal!: any;

  public clauseName: string = "";
  public clause: any;
  public avtiveTab: string = "lienquan";
  public clauseId: string = "";
  public totalClause: number = 0;
  public conclusion: any = "";
  public listDieuKhoanLienQuan: any = [];
  public listDieuKhoanLienQuanFilter: any = [];
  public listThamQuyenNoiDung: any = [];
  public isToggleAddClause: boolean = false;
  public isShowTable: boolean = false;
  public ColumnMode = ColumnMode;
  public sizePage = [12, 20, 30, 100];
  public limit: number = this.sizePage[0];
  public loadingIndex = null;
  public unSubAll: Subject<any> = new Subject();
  public clauseCompared: any; //clause được so sánh
  public clauseCompare: any; //clause so sánh
  public fileInfor; //  file hiển thị trên view detail file
  public isEditClause: boolean = false;
  public totalRelateClause: number = 0;
  public page: number = 1;
  public totalItem: number;
  public hardTotalItem: number;
  private _unSubAll: Subject<any> = new Subject();
  public textSearchRelateClause: FormControl = new FormControl("");
  public mapById: Record<number, any> = {};
  public isBatchLoading = false;
  public progress = { done: 0, total: 0, percentage: 0 };
  public loadingIds = new Set<number>();
  private batchLoadingIds = new Set<number>();

  states = this.progressToastStore.states$;
  constructor(
    private documentService: ListDocumentService,
    private detailClauseService: DetailClauseService,
    private compareChatbotService: ComnpareClauseChatbotService,
    private viewDetailFile: ViewDetailFileService,
    private router: Router,
    private _toastrService: ToastrService,
    private bosungVanBan: BoSungVanBanDieuKhoanService,
    private workSpaceService: DetailWorkSpaceService,
    private modalService: NgbModal,
    private route: ActivatedRoute,
    private webSocketService: WebSocketService,
    private progressToastStore: ProgressToastStore
  ) {}

  ngOnInit(): void {
    this.webSocketService.messageSubject
      .pipe(takeUntil(this._unSubAll))
      .subscribe((res) => {
        if (!res) return;

        const evt = res?.data?.event || res?.event;
        const data = res?.data?.data || res?.data || {};
        const jobId = this.progressToastStore.resolveJobId(data);
        const pct = this.progressToastStore.clampPercent(
          res?.data?.percentage ?? res?.percentage ?? data?.percent ?? 0
        );
        const targetId = Number(data?.origin_clause_id || 0);
        // console.log("res", res);
        // console.log("targetId", targetId);
        switch (evt) {
          case "AUTO_COMPARE_START": {
            this.batchLoadingIds.add(targetId);
            this.isBatchLoading = true;
            this.progress = {
              done: 0,
              total: Number(data?.total || 0),
              percentage: 0,
            };
            // console.log("ddd", data?.items);
            for (const x of data?.items || []) {
              this.loadingIds.add(Number(x.id));
            }
            this.applyViewStateToPage();
            this._toastrService.success(`${data?.message}`, "Thông báo", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              enableHtml: true,
            });
            break;
          }

          case "AUTO_COMPARE_ITEM_UPDATE": {
            // console.log("AUTO_COMPARE_ITEM_UPDATE", res)
            const id = Number(data?.clause_id);
            const status = data?.status as "success" | "error";

            this.loadingIds.delete(id);

            const cur = this.listDieuKhoanLienQuanFilter.find(
              (it: any) => it.id === id
            );

            if (cur) {
              for (const it of this.listDieuKhoanLienQuanFilter) {
                if (it.id === cur.id && status === "success") {
                  it.reason = data.item.reason;
                  it.ly_do = data.item.ly_do;
                  it.ket_luan = data.item.ket_luan;
                  it.giai_phap = data.item.giai_phap;
                  it.tien_trinh_tu_duy = data.item.tien_trinh_tu_duy;
                }
              }

              cur.isLoading = false;
              cur.status = status;
              if (status === "success") {
                cur.error = undefined;
              } else {
                cur.error = data?.error;
              }
            }
            // progress tổng
            const p = data?.progress || {};
            this.progress = {
              done: Number(p.done || 0),
              total: Number(p.total || this.progress.total || 0),
              percentage: Number(p.percentage || 0),
            };
            if (
              this.progress.total &&
              this.progress.done >= this.progress.total
            ) {
              // this.isBatchLoading = false;
            }

            break;
          }

          case "AUTO_COMPARE_RESET_ALL": {
            this.batchLoadingIds.delete(targetId);
            this.isBatchLoading = false;
            this.loadingIds.clear();
            for (const it of this.listDieuKhoanLienQuanFilter)
              it.isLoading = false;
            break;
          }

          case "AUTO_COMPARE_DONE": {
            // console.log("targetId", targetId);
            // console.log("this.batchLoadingIds", this.batchLoadingIds);
            this.batchLoadingIds.delete(targetId);
            // this.isBatchLoading = false;
            // this.loadingIds.clear();
            // for (const it of this.listDieuKhoanLienQuanFilter)
            //   it.isLoading = false;
            this._toastrService.success(`${data?.message}`, "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              enableHtml: true,
            });
            break;
          }

          case "AUTO_COMPARE_ERROR": {
            this.batchLoadingIds.delete(targetId);
            // this.isBatchLoading = false;
            this.loadingIds.clear();
            for (const it of this.listDieuKhoanLienQuanFilter)
              it.isLoading = false;

            this._toastrService.error(`${data?.message}`, "Thất bại", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              enableHtml: true,
            });
            break;
          }

          case "AUTO_COMPARE_WARING": {
            this.batchLoadingIds.delete(targetId);
            // this.isBatchLoading = false;
            for (const it of this.listDieuKhoanLienQuanFilter)
              it.isLoading = false;
            this._toastrService.warning(`${data?.message}`, "Cảnh báo", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              enableHtml: true,
            });
            break;
          }
          case "EXPORT_FILE_START": {
            // if (data?.type === "cap_dieu_khoan") {
            //   this._toastrService.success(`${data?.message}`, "Thông báo", {
            //     closeButton: true,
            //     positionClass: "toast-top-right",
            //     toastClass: "toast ngx-toastr",
            //     enableHtml: true,
            //   });
            // }
            break;
          }
          default:
            break;
        }

        if (
          evt === "AUTO_COMPARE_DONE" ||
          evt === "AUTO_COMPARE_WARING" ||
          evt === "AUTO_COMPARE_ERROR"
        ) {
          this.isButtonLoading(Number(this.clauseId));
          // console.log("this.isBatchLoading",this.isBatchLoading);
          // if (!this.isBatchLoading) {
          //   this.getDieuKhoanLienQuan(this.clauseId);
          //   this.getThamQuyenNoiDung(this.clauseId);
          //   this.getDetailClauseById(this.clauseId);
          // }
          this.getDieuKhoanLienQuan(this.clauseId);
          this.getThamQuyenNoiDung(this.clauseId);
          this.getDetailClauseById(this.clauseId);
        }
      });
    this.compareChatbotService.singleCompareFinished$
      .pipe(takeUntil(this.unSubAll))
      .subscribe(({ id, result }) => {
        const it = this.listDieuKhoanLienQuanFilter.find(
          (x: any) => x.id === id
        );
        if (it) it.isLoading = false;

        // cho phép bấm item khác
        this.detailClauseService.idClause.next(null);
      });
    this.route.queryParams
      .pipe(takeUntil(this.unSubAll))
      .subscribe((params) => {
        this.clauseId = params["clauseId"];
      });
    this.detailClauseService.currentPage
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.page = res;
      });

    this.documentService.clauseValue
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res: any) => {
        // xoá đi đợi load xong data mới thì hiện cái mới lên
        this.conclusion = "";
        this.listDieuKhoanLienQuan = [];
        this.listDieuKhoanLienQuanFilter = [];
        this.listThamQuyenNoiDung = [];
        // xoá đi đợi load xong data mới thì hiện cái mới lên
        this.clauseName = res.title;
        // console.log("3", this.textSearchRelateClause.value);
        this.textSearchRelateClause.patchValue("");
        this.clauseId = res.id;
        // console.log("chuyen qua");
        this.isButtonLoading(Number(res.id));
        this.getDieuKhoanLienQuan(res.id);
        this.getThamQuyenNoiDung(res.id);
        this.getDetailClauseById(res.id);
      });
    this.detailClauseService.textSearch
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        if (res) {
          this.textSearchRelateClause.patchValue(res);
        }
      });

    this.compareChatbotService.clauseInfo1
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.clauseCompared = res;
      });
    this.detailClauseService.isShowtable
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.isShowTable = res;
      });
    this.detailClauseService.isSaveResoultFromChatBot
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        if (res) {
          this.getDieuKhoanLienQuan(this.clauseId);
          this.getThamQuyenNoiDung(this.clauseId);
        }
      });
    this.detailClauseService.idClause
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.loadingIndex = res;
      });
    this.viewDetailFile.fileInfor
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.fileInfor = res;
        // console.log("res", res);
      });
    this.textSearchRelateClause.valueChanges
      .pipe(takeUntil(this.unSubAll), debounceTime(600), distinctUntilChanged())
      .subscribe((res) => {
        console.log("1");
        this.page = 1;
        this.detailClauseService.currentPage.next(1);
        this.getDieuKhoanLienQuan(this.clauseId);
      });
  }
  private reindex() {
    this.mapById = {};
    for (const it of this.listDieuKhoanLienQuanFilter) {
      this.mapById[it.id] = it;
    }
  }
  isButtonLoading(id: number) {
    this.isBatchLoading = this.batchLoadingIds.has(id);
    // console.log("this.isBatchLoading", this.isBatchLoading);
    // console.log("this.batchLoadingIds", this.batchLoadingIds);
  }
  isLoading(id: number): boolean {
    return this.loadingIds.has(id);
  }
  applyViewStateToPage() {
    for (const it of this.listDieuKhoanLienQuanFilter) {
      it.isLoading = this.loadingIds.has(it.id);
    }
  }
  getDieuKhoanLienQuan(idClause: string) {
    const params = {
      page: this.page,
      search: this.textSearchRelateClause.value,
      ket_luan: this.conclusion || "",
    };
    this.detailClauseService.getDieuKhoanLienQuan(idClause, params).subscribe(
      (res) => {
        this.totalItem = res.count;
        if (!params.search && !params.ket_luan) {
          this.hardTotalItem = res.count;
        }
        this.detailClauseService.idClause.next(null);
        this.loadingIndex = null;
        if (res.results) {
          this.listDieuKhoanLienQuan = res.results;
          this.listDieuKhoanLienQuanFilter = res.results;
          this.totalRelateClause = res.count;
          this.applyViewStateToPage();
        } else {
          this.listDieuKhoanLienQuan = [];
          this.listDieuKhoanLienQuanFilter = [];
        }
      },
      (error) => {
        this.listDieuKhoanLienQuan = [];
        this.listDieuKhoanLienQuanFilter = [];
      }
    );
  }
  getThamQuyenNoiDung(idClause: string) {
    this.detailClauseService.getThamQuyenNoiDung(idClause).subscribe(
      (res) => {
        this.detailClauseService.idClause.next(null);
        this.loadingIndex = null;
        if (res) this.listThamQuyenNoiDung = res;
        else this.listThamQuyenNoiDung = [];
      },
      (error) => {
        this.listThamQuyenNoiDung = [];
      }
    );
  }
  getDetailClauseById(idClause: string) {
    this.detailClauseService.getDetailClauseById(idClause).subscribe(
      (res) => {
        this.clause = res;
        this.compareChatbotService.clauseInfo1.next(res); // cập nhật thông tin vào trong chatbot so sánh
      },
      (error) => {
        this._toastrService.error("Lấy chi tiết điều khoản thất bại", "Lỗi", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    );
  }
  changeNav(event) {
    const tabs = event.nextId;
    this.avtiveTab = tabs;
    if (this.textSearchRelateClause.value != "")
      this.textSearchRelateClause.patchValue("");
  }
  goBack() {
    this.documentService.goBack();
    // this.documentService.contentValue.next(ShowContent.Document);
  }
  addRelateClause() {
    this.isToggleAddClause = !this.isToggleAddClause;
    this.bosungVanBan.typeSearch.next(true);
    this.documentService.rightSideBarValue.next(
      this.isToggleAddClause ? ShowSideBar.AddClause : ShowSideBar.Note
    );
  }
  compareClause(item) {
    for (const it of this.listDieuKhoanLienQuanFilter) {
      if (item.id === it.id) {
        it.isLoading = true;
      }
    }
    this.workSpaceService.isNotesCollapsed.next(false); // mở phần collab bên phải ra nêu đang đóng

    if (this.loadingIndex == null)
      this.detailClauseService.idClause.next(item.id);
    else {
      return; // chặn khi đang so sánh rồi
    }
    const clauseTemp = { ...item }; // clone shallow
    clauseTemp.ly_do = null; // để sang bên component so sánh chạy lại so sánh
    this.documentService.rightSideBarValue.next(ShowSideBar.CompareClause);
    this.compareChatbotService.clauseInfo2.next(clauseTemp);
    this.compareChatbotService.clauseTerm.next(null);
  }

  openConfirmModal() {
    Swal.fire({
      title: "So sánh tất cả",
      html: `<div class="d-flex align-items-center flex-column justify-content-center">
      <span>Thực hiện so sánh với <strong>${this.hardTotalItem} điều khoản liên quan.</strong><span>
      Kết quả so sánh sẽ tự động lưu.
    </div>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Xác nhận",
      cancelButtonText: '<span style="color:black;">Huỷ</span>',
      reverseButtons: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
    }).then((result) => {
      if (result.isConfirmed) {
        this.autoCompareClauseAll();
      } else if (result.isDismissed) {
      }
    });
  }

  autoCompareClauseAll() {
    const body = {
      api_url: environment.apicompare,
      clause_id: this.clauseId,
      item: this.compareChatbotService.clauseInfo1.value,
    };
    this.detailClauseService
      .autoCompareAllRelated(this.clauseId, body)
      .subscribe({
        next: (res: any) => {
          // console.log("res", res);
          // this.toast.info(`${res.message}`, "Thông báo", {
          //   closeButton: true,
          //   positionClass: "toast-top-right",
          //   toastClass: "toast ngx-toastr",
          // });
        },
        error: (err) => {
          // this.toast.error(`Có lỗi xảy ra!`, "Thất bại", {
          //   closeButton: true,
          //   positionClass: "toast-top-right",
          //   toastClass: "toast ngx-toastr",
          // });
        },
      });
  }
  downloadReport() {
    const current_user = JSON.parse(localStorage.getItem("current_User"));
    const params = {
      type: "cap_dieu_khoan",
      user_id: current_user.id,
    };
    this.detailClauseService.getFileReport(this.clauseId, params).subscribe(
      (res) => {},
      (error) => {}
    );
  }
  viewFileDieuKhoanLienQuan(fileLienQuan) {
    this.documentService.setBehavior(ShowContent.Clause);
    this.documentService.FileSearchTemp.next(fileLienQuan);
    this.detailClauseService.isShowtable.next(this.isShowTable);
    this.viewDetailFile.clauseId2.next(fileLienQuan.clause_id); //scroll đến điều được chọn
    this.detailClauseService.textSearch.next(this.textSearchRelateClause.value);
    this.router.navigate([], {
      queryParams: {
        fileId: fileLienQuan.doc_id,
        tabs: "toanvan",
        luocdo: "es",
        time: new Date().getTime(),
        fileName: fileLienQuan.document_data
          ? fileLienQuan.document_data.title
          : fileLienQuan.title,
        save: true,
        type: "search"
      },
      queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
    });
  }
  onActivate(event) {
    if (
      event.event.type === "click" &&
      event.column.name != "Hành động" &&
      event.column.name != "Chi tiết so sánh" &&
      event.column.name != "HÀNH ĐỘNG" &&
      event.column.name != "ACTION" &&
      event.column.name != "Action" &&
      event.column.name != "checkbox" &&
      event.column.name != ""
    ) {
      const fileLienQuan = event.row;
      this.documentService.setBehavior(ShowContent.Clause);
      this.documentService.FileSearchTemp.next(fileLienQuan);
      this.viewDetailFile.clauseId2.next(fileLienQuan.clause_id); //scroll đến điều được chọn

      // this.viewDetailFile.fileName.next(fileLienQuan.document_data.title);
      this.detailClauseService.isShowtable.next(this.isShowTable);
      this.detailClauseService.textSearch.next(
        this.textSearchRelateClause.value
      );
      this.router.navigate([], {
        queryParams: {
          fileId: fileLienQuan.doc_id,
          tabs: "toanvan",
          luocdo: "es",
          // type: "search",
          time: new Date().getTime(),
          fileName: fileLienQuan.document_data.title,
          save: true,
          type: "search"
        },
        queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
      });
    }
  }
  rowDetailsToggleExpand(row) {
    this.tableRowDetails.rowDetail.toggleExpandRow(row);
  }
  deleteClause(clause) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this.detailClauseService.deleteClause(clause.id).subscribe({
          next: (res) => {
            this._toastrService.success(
              "Điều khoản đã được xóa thành công!",
              "Thành công",
              {
                closeButton: true,
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
              }
            );
            this.getDieuKhoanLienQuan(this.clauseId);
          },
          error: (err) => {
            this._toastrService.error(
              "Xóa điều khoản thất bại. Vui lòng thử lại!",
              "Lỗi",
              {
                closeButton: true,
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
              }
            );
          },
        });
      }
    });
  }
  viewDetailCompare(clause) {
    this.workSpaceService.isNotesCollapsed.next(false); // mở phần collab bên phải ra nêu đang đóng
    if (this.loadingIndex != null) return; // chặn khi đang so sánh rồi
    this.documentService.rightSideBarValue.next(ShowSideBar.CompareClause);
    const clauseTemp = { ...clause }; // clone shallow
    clauseTemp.reason = null;
    this.compareChatbotService.clauseInfo2.next(clauseTemp);
    this.compareChatbotService.clauseTerm.next("x"); // để k bị so sánh lại
  }
  showModalDetailCompare(clause) {
    console.log("clause", clause);

    this.clauseCompare = clause;
    this.modalService.open(this.detailCompareModal, {
      centered: true,
      size: "xl",
    });
  }
  changeStatusClasue(statusClause) {
    const formData = new FormData();

    formData.append("ly_do", this.clauseCompare.ly_do);
    formData.append("ket_luan", statusClause);
    formData.append("giai_phap", this.clauseCompare.giai_phap);
    formData.append("tien_trinh_tu_duy", this.clauseCompare.giai_phap);

    if (formData) {
      const id = this.clauseCompare.id;
      this.compareChatbotService.saveResoultCompare(id, formData).subscribe(
        (res) => {
          this.getDieuKhoanLienQuan(this.clauseId);
          this.modalService.dismissAll();
          this._toastrService.success("Cập nhật thành công", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          const clauseTemp = { ...this.clauseCompare }; // clone shallow
          clauseTemp.reason = null;
          clauseTemp.ket_luan = statusClause;
          this.compareChatbotService.clauseInfo2.next(clauseTemp); // cập nhật thông tin vào trong chatbot so sánh
        },
        (error) => {
          this._toastrService.error("Cập nhật thất bại", "Thất bại", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        }
      );
    }
  }
  toggleEdit() {
    this.isEditClause = !this.isEditClause;
  }

  saveClause(diendai, giaiphap, tuduy) {
    const lyDo = diendai.innerText.trim();
    const giaiPhap = giaiphap.innerText.trim();
    const tuDuy = tuduy.innerText.trim();
    const formData = new FormData();

    formData.append("ly_do", lyDo);
    formData.append("ket_luan", this.clauseCompare.ket_luan);
    formData.append("giai_phap", giaiPhap);
    formData.append("tien_trinh_tu_duy", tuDuy);
    if (formData) {
      const id = this.clauseCompare.id;
      this.compareChatbotService.saveResoultCompare(id, formData).subscribe(
        (res) => {
          this.getDieuKhoanLienQuan(this.clauseId);
          this.modalService.dismissAll();
          this.isEditClause = false;
          this._toastrService.success("Cập nhật thành công", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          const clauseTemp = { ...this.clauseCompare }; // clone shallow
          clauseTemp.reason = null;
          clauseTemp.ly_do = lyDo;
          clauseTemp.giai_phap = giaiPhap;
          clauseTemp.tien_trinh_tu_duy = tuDuy;
          this.compareChatbotService.clauseInfo2.next(clauseTemp); // cập nhật thông tin vào trong chatbot so sánh
        },
        (error) => {
          this._toastrService.error("Cập nhật thất bại", "Thất bại", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        }
      );
    }
  }

  filterByConclusion(event) {
    const value = (event.target as HTMLSelectElement).value;
    this.conclusion = value;
    this.page = 1;
    this.detailClauseService.currentPage.next(1);
    this.getDieuKhoanLienQuan(this.clauseId);
  }
  copyText(text) {
    navigator.clipboard.writeText(text);
    this._toastrService.success("Sao chép", "Thành công", {
      closeButton: true,
      positionClass: "toast-top-right",
      toastClass: "toast ngx-toastr",
    });
  }
  showContentClause() {
    this.modalService.open(this.contentClauseModal, {
      centered: true,
      size: "lg",
    });
  }
  onPageChange(event) {
    this.page = event;
    this.detailClauseService.currentPage.next(event);
    this.getDieuKhoanLienQuan(this.clauseId);
  }
  setPage(event) {
    const page = event.offset + 1;
    console.log(page);

    this.page = page;
    this.detailClauseService.currentPage.next(page);
    this.getDieuKhoanLienQuan(this.clauseId);
  }
  raSoatTatCa() {}
  ngOnDestroy() {
    this.unSubAll.next(null);
    this.unSubAll.complete();
    this.documentService.rightSideBarValue.next(ShowSideBar.Note);
    this._unSubAll.next(null);
    this._unSubAll.complete();
  }
}
