import { format, isValid, parse } from "date-fns";
export class FormatTime {
  // dd-MM-yyyy | dd-MM-yyyy HH:mm | dd-MM-yyyy HH:mm:ss
  public formatDateTime(value: any, formatStr: string): string {
    if (!value) return "";

    let dateObj: Date | null = null;

    // 1. Nếu là Date
    if (value instanceof Date) {
      dateObj = value;
    }
    // 2. Nếu là timestamp (number)
    else if (typeof value === "number") {
      dateObj = new Date(value);
    }
    // 3. Nếu là string
    else if (typeof value === "string") {
      // Thử parse theo các định dạng thường gặp
      const possibleFormats = [
        "dd/MM/yyyy",
        "MM/dd/yyyy",
        "yyyy-MM-dd",
        "dd-MM-yyyy",
        "yyyy/MM/dd",
      ];

      for (const fmt of possibleFormats) {
        const parsed = parse(value, fmt, new Date());
        if (isValid(parsed)) {
          dateObj = parsed;
          break;
        }
      }

      // Nếu vẫn null, thử parse theo Date constructor (ISO string…)
      if (!dateObj) {
        const parsed = new Date(value);
        if (isValid(parsed)) {
          dateObj = parsed;
        }
      }
    }

    // Kiểm tra kết quả
    if (dateObj && isValid(dateObj)) {
      return format(dateObj, formatStr);
    } else {
      return "Invalid date";
    }
  }
}
