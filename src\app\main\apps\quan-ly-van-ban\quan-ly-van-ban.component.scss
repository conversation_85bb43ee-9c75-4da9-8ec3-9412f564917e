@import "@core/scss/angular/libs/datatables.component.scss";
@import "@core/scss/angular/libs/select.component.scss";
@import "@core/scss/angular/libs/date-time-picker.component.scss";
@import "@core/scss/angular/libs/flatpickr.component.scss";
@import "~diff2html/bundles/css/diff2html.min.css";
@import "@core/scss/angular/libs/tour.component.scss";

.datatable-body-cell-label {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;
  // justify-content: space-between;
  // display: flex;
}
.workspace-item {
  border-radius: 0.375rem;
  cursor: pointer;
}
.workspace-item:hover {
  background: rgba(245, 247, 251, 1);
}
.swal-cancel-info {
  border: 2px solid #3085d6 !important;
  color: #3085d6 !important;
  background-color: white !important;
  padding: 8px 20px !important;
  border-radius: 5px !important;
  transition: all 0.3s;
}

.swal-cancel-info:hover {
  background-image: linear-gradient(
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0)
  ) !important;
  box-shadow: 0 8px 16px rgba(48, 133, 214, 0.4) !important;
  transform: translateY(-3px);
}

.swal-confirm-danger {
  transition: all 0.3s;
}

.swal-confirm-danger:hover {
  box-shadow: 0 8px 16px rgba(211, 51, 51, 0.4) !important;
  transform: translateY(-3px);
}
.d2h-code-side-line del {
  // text-decoration: underline !important;
  font-weight: 700 !important;
  background-color: #ffffff00 !important;
  color: red !important;
  // color: rgba(255, 0, 0, 0.315) !important;
}
.d2h-code-side-line ins {
  // text-decoration: underline !important;
  font-weight: 700 !important;
  background-color: #ffffff00 !important;
  color: green !important;
}
.d2h-code-line-ctn {
  background-color: #ffffff00 !important;
  color: rgb(0, 0, 0) !important;
}
.diff-header {
  display: flex;
  justify-content: space-around;
  font-size: 17px;
  font-weight: 600;
}
.d2h-file-header {
  display: none;
}
.d2h-info {
  display: none;
}
.d2h-emptyplaceholder {
  display: none;
}
.ng-select .ng-select-container .ng-value-container .ng-placeholder {
  color: #333 !important;
}
.workspace {
  background-color: rgba(243, 248, 255, 1);
  padding: 12px 16px;
  border-radius: 12px;
  height: 100%;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.15);
}
.btn-chatbot {
  position: fixed;
  bottom: 4%;
  right: 2%;
  background-color: #008fe3;
  border-radius: 50%;
  padding: 10px;
  cursor: pointer;
}
.menu-item {
  position: relative;
  cursor: pointer;
  opacity: 0.5;
}

.menu-item::after {
  content: "";
  position: absolute;
  bottom: -1rem; /* cách chữ 2rem xuống dưới */
  left: 0;
  width: 30%;
  height: 2px;
  background-color: #000000b2;
  opacity: 0;
}
.menu-item.active {
  opacity: 1; /* Chữ rõ khi active */
}
.menu-item.active::after {
  opacity: 1;
}
.chat-button {
  background: #008fe3;
  border: none;
  padding: 5px;
  outline: none;
}

.chat-container {
  position: relative;
  display: inline-block;
  padding: 10px;
}

.chat-icon {
  width: 40px; /* tùy chỉnh kích thước */
  height: auto;
}

.dots {
  position: absolute;
  bottom: 25px; /* chỉnh vị trí so với icon */
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  background-color: #008fe3; /* màu dấu chấm */
  border-radius: 50%;
  animation: bounce 0.6s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: 0s;
}
.dot:nth-child(2) {
  animation-delay: 0.2s;
}
.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
}
.no-workspace {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.show-tour-guide {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  padding: 10px;
  background: linear-gradient(
      0deg,
      rgba(25, 117, 210, 0.1) 0%,
      rgba(25, 117, 210, 0.1) 100%
    ),
    #fff;
}

.quan-ly-van-ban-action-button {
  min-width: 135px;
}

.quan-ly-van-ban-compare-icon {
  width: 14px;
}

.quan-ly-van-ban-sort-select {
  min-width: 150px;
}

.quan-ly-van-ban-workspace-title {
  font-size: 16px;
  color: rgba(33, 33, 33, 1);
}

.quan-ly-van-ban-workspace-date {
  text-align: end;
  color: rgba(97, 97, 97, 1);
}

.search-custom-1{
  position:absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  width: 20px;   /* mặc định là 16 */
  height: 20px;
  color:grey;
  
}

input.form-control.input-custom-1 {
  min-width: 41x !important;

}

@media (max-width: 767.98px) {
  input#basicInput {
    padding-left: 2rem; /* chừa chỗ cho icon */
  }
}
